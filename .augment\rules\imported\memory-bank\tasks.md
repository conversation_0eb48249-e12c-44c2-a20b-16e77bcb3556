---
type: "manual"
---

# Finema UI Kit - Task Documentation

This file documents repetitive tasks and workflows for the Finema UI Kit project. These tasks follow similar patterns and require editing the same types of files.

## Add New Form Input Component

**Last performed:** N/A (template for future reference)

**Files to modify:**
- `/src/runtime/components/Form/Input[Name]/index.vue` - Create new input component
- `/src/runtime/components/Form/Input[Name]/types.ts` - Add component type definitions
- `/src/runtime/components/Form/types.ts` - Add to INPUT_TYPES enum and IFormField union
- `/src/runtime/components/Form/Fields.vue` - Add component to dynamic renderer
- `/playground/pages/form.vue` - Add demo example for testing

**Steps:**
1. Create new component directory under `/src/runtime/components/Form/`
2. Implement component following existing patterns (props, emits, composables)
3. Create type definitions with field interface extending IFormFieldBase
4. Add new INPUT_TYPE enum value and include in IFormField union type
5. Update Fields.vue component renderer to handle new input type
6. Add comprehensive demo in playground with validation examples
7. Write unit tests if component has complex logic
8. Update documentation

**Important notes:**
- Follow existing naming conventions (Input[Type] format)
- Ensure accessibility compliance (ARIA labels, keyboard navigation)
- Maintain consistency with theme system
- Test with various validation scenarios
- Consider mobile responsive design

## Add New Utility Helper Class

**Last performed:** N/A (template for future reference)

**Files to modify:**
- `/src/runtime/utils/[Name]Helper.ts` - Create new helper class
- `/src/runtime/utils/[Name]Helper.spec.ts` - Add comprehensive tests
- Auto-imported through module configuration

**Steps:**
1. Create new helper class with static methods following existing patterns
2. Write comprehensive unit tests covering all methods and edge cases
3. Add JSDoc comments for all public methods
4. Ensure TypeScript type safety throughout
5. Test with Vitest to verify all tests pass
6. Consider integration with existing helpers if applicable

**Important notes:**
- Follow existing class naming pattern ([Name]Helper)
- Use static methods for utility functions
- Provide comprehensive test coverage (aim for 100%)
- Include error handling for edge cases
- Document complex logic with comments

## Add New Composable

**Last performed:** N/A (template for future reference)

**Files to modify:**
- `/src/runtime/composables/use[Name].ts` - Create new composable
- `/playground/pages/[name].vue` - Add demo page (optional)
- Tests if complex logic is involved

**Steps:**
1. Create composable following Vue 3 Composition API patterns
2. Use TypeScript interfaces for return types and parameters
3. Follow existing naming convention (use[Name] format)
4. Ensure reactive state management where appropriate
5. Add demo usage in playground if beneficial
6. Document usage patterns and examples

**Important notes:**
- Return reactive references and computed values where needed
- Follow existing patterns for error handling and loading states
- Consider integration with other composables (useNotification, useDialog)
- Ensure cleanup of side effects when component unmounts

## Update Theme Configuration

**Last performed:** N/A (template for future reference)

**Files to modify:**
- `/src/runtime/theme/[component].ts` - Update specific component theme
- `/src/runtime/theme/index.ts` - Export new theme if adding new file
- `/src/module.ts` - Update if global theme changes needed

**Steps:**
1. Follow existing theme structure and patterns
2. Ensure compatibility with Tailwind CSS and @nuxt/ui
3. Test theme changes in playground environment
4. Maintain consistency with overall design system
5. Document any breaking changes to theme structure

**Important notes:**
- Maintain backward compatibility when possible
- Test theme changes across all components
- Consider dark mode compatibility
- Follow Tailwind CSS naming conventions

## Release New Version

**Last performed:** Version 2.20.0

**Files to modify:**
- Version bump happens automatically via changelogen
- `CHANGELOG.md` - Updated automatically
- `package.json` - Version updated automatically

**Steps:**
1. Ensure all tests pass: `bun run test`
2. Ensure linting passes: `bun run lint`
3. Review and commit all changes
4. Run release command: `bun run release`
5. Verify publication to npm registry
6. Check that git tags are pushed correctly

**Important notes:**
- Follow semantic versioning (MAJOR.MINOR.PATCH)
- Release command handles: lint → test → build → changelog → publish → git push
- Ensure all breaking changes are documented
- Test installation of published package in separate project
