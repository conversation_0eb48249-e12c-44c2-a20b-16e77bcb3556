# Finema UI Kit Project Brief

## Project Overview
Building a comprehensive Vue.js UI component library called [`@finema/core`](package.json:2) specifically designed for Nuxt applications. This is an enterprise-grade UI kit developed by the Finema Dev Core Team for creating consistent and beautiful user interfaces across Finema projects.

## Core Objectives
- Provide a complete set of reusable Vue components optimized for Nuxt framework
- Ensure consistent design language and user experience across all Finema applications
- Deliver accessibility-focused components with responsive design out-of-the-box
- Integrate seamlessly with Nuxt's auto-import capabilities and modern development workflow

## Key Features
- **Comprehensive Component Library**: Forms, tables, dialogs, notifications, file uploads, WYSIWYG editors
- **Framework Integration**: Built specifically for Nuxt 3 with TypeScript support
- **Design System**: Consistent theming using Tailwind CSS and [`@nuxt/ui`](src/module.ts:92)
- **Developer Experience**: Auto-imports, type safety, and comprehensive playground for testing
- **Validation**: Integrated with [`vee-validate`](src/module.ts:108) and [`valibot`](src/module.ts:174) for robust form handling

## Target Users
- Finema development teams building Vue.js/Nuxt applications
- Developers who need consistent, enterprise-ready UI components
- Projects requiring accessibility compliance and responsive design

## Success Criteria
- Easy installation and integration with existing Nuxt projects
- Comprehensive component coverage for common UI patterns
- Excellent developer experience with TypeScript support
- Maintainable and extensible codebase
- Active playground environment for component testing and development
