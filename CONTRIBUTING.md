# Contributing to Our Project

Thank you for considering contributing to our project! We appreciate your time and effort.

## Introduction

[Brief overview of the project and its goals. Explain why contributions are valuable.]

## Getting Started

### Prerequisites

- Node.js (specify version if known, e.g., v18.x or higher)
- bun (Package Manager)

### Setting Up the Development Environment

1.  **Fork the repository:** [https://gitlab.finema.co/finema/ui-kit](https://gitlab.finema.co/finema/ui-kit

## Commit Message Guidelines

When writing commit messages, please follow these guidelines to ensure consistency and clarity:

- Use the present tense ("Add feature" not "Added feature").
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...").
- Limit the first line to 72 characters or less.
- Reference issues and pull requests liberally after the first line.
- Consider starting the commit message with an applicable emoji:
  - :sparkles: `:sparkles:` when introducing new features.
  - :bug: `:bug:` when fixing a bug.
  - :memo: `:memo:` when writing docs.
  - :art: `:art:` when improving the format/structure of the code.
  - :recycle: `:recycle:` when refactoring code.
  - :white_check_mark: `:white_check_mark:` when adding tests.
  - :wrench: `:wrench:` when performing maintenance.
  - :lipstick: `:lipstick:` when updating the UI and style files.

Common types include: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`, `ci`, `build`.

### Examples

Here are some examples of how to format your commit messages for this project:

*   **New Feature (e.g., for a Form component):**
    ```
    feat(Form): add support for custom validation rules
    ```
*   **Bug Fix (e.g., for a Table component):**
    ```
    fix(Table): correct pagination display for empty datasets

    This resolves an issue where the pagination controls were incorrectly
    rendered when the table had no data, leading to a confusing UX.
    ```
*   **Documentation (e.g., for a Dialog component):**
    ```
    docs(Dialog): clarify usage of the modal prop
    ```
*   **Refactoring (e.g., for a FlexDeck component):**
    ```
    refactor(FlexDeck): optimize rendering performance for many items
    ```
*   **Tests (e.g., for a StringHelper utility):**
    ```
    test(StringHelper): add more test cases for kebab-case conversion
    ```
*   **Chore (e.g., dependency update):**
    ```
    chore: upgrade Nuxt to v3.17.3
    ```
*   **Styling (e.g., for a Button component):**
    ```
    style(Button): update primary button color to match new branding
    ```
*   **Performance (e.g., for the Requester library):**
    ```
    perf(Requester): reduce bundle size by removing unused utility
    ```
