## App (Component)

### Overview
Application wrapper providing Nuxt UI context, global loading indicator, and developer tools in dev.

### Props
- toaster?: ToasterProps | null — passed to UApp

### Behavior
- NuxtLoadingIndicator color bound to coreConfig.color
- UApp locale set to Thai (th)
- In dev: renders DevToolsWindow inside DevOnly
- Document title template includes coreConfig.site_name

### Usage
```vue
<App :toaster="{ position: 'top-right' }">
  <NuxtPage />
</App>
```

