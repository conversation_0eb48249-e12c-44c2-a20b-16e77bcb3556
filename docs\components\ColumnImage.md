## Table/ColumnImage (Component)

### Overview
A specialized table column component that renders image thumbnails within table cells. It uses the underlying Image component to display images with built-in loading and error states, making it perfect for displaying profile pictures, product images, or any visual content in data tables.

### Registration
- Auto-registered by @finema/core
- Used automatically when column type is `COLUMN_TYPES.IMAGE`
- Optional prefix via core.prefix (e.g., `<F-ColumnImage>`)

### Props
- **value**: `any` - The image URL or source path (typically a string)
- **row**: `any` - The complete row data object from the table
- **column**: `TableColumn<any>` - The column configuration object

### Features
- **Fixed Dimensions**: Consistent `h-12` (48px) height for uniform table appearance
- **Rounded Corners**: Applies `rounded` class for modern aesthetic
- **Loading States**: Inherits loading spinner from the Image component
- **Error Handling**: Shows error icon when image fails to load
- **Responsive**: Maintains aspect ratio within the fixed height constraint

### Styling
- **Height**: Fixed at `h-12` (48px) for consistent table row heights
- **Border Radius**: `rounded` class for subtle corner rounding
- **Width**: Auto-adjusts to maintain aspect ratio within height constraint

### Usage in Tables

#### Basic Usage with useTable
```vue
<script setup lang="ts">
import { COLUMN_TYPES } from '#core/components/Table/types'

interface TableItem {
  id: string
  name: string
  profilePic: string
  // ... other fields
}

const tableOptions = useTable<TableItem>({
  repo: store,
  columns: () => [
    { accessorKey: 'name', header: 'Name' },
    {
      accessorKey: 'profilePic',
      header: 'Profile Picture',
      type: COLUMN_TYPES.IMAGE
    },
    // ... other columns
  ],
})
</script>

<template>
  <Table :options="tableOptions" />
</template>
```

#### Direct Component Usage
```vue
<template>
  <ColumnImage
    :value="imageUrl"
    :row="rowData"
    :column="columnConfig"
  />
</template>
```

### Data Requirements
The `value` prop should contain a valid image URL or path:

```typescript
// Example data structure
const tableData = [
  {
    id: '1',
    name: 'John Doe',
    profilePic: 'https://example.com/avatar.jpg', // ← This becomes the value prop
    email: '<EMAIL>'
  },
  {
    id: '2',
    name: 'Jane Smith',
    profilePic: '/images/jane-avatar.png', // ← Relative paths work too
    email: '<EMAIL>'
  }
]
```

### Error Handling
When an image fails to load, the component automatically displays:
- Error icon (exclamation circle)
- Consistent sizing within the table cell
- Graceful fallback that doesn't break table layout

### Loading States
While images are loading, the component shows:
- Centered loading spinner
- Consistent cell dimensions
- Smooth transition to loaded state

### Integration with Image Component
ColumnImage leverages the full power of the base Image component:
- Built on `@vueuse/components UseImage`
- Automatic loading and error state management
- Optimized performance with lazy loading capabilities

### Related Components
- **[Image](./Image.md)** - Base image component with loading/error states
- **[Table](./Table.md)** - Main table component system
- **[ColumnText](./ColumnText.md)** - Text column renderer
- **[ColumnDate](./ColumnDate.md)** - Date column renderer
- **[ColumnNumber](./ColumnNumber.md)** - Number column renderer

### Best Practices
1. **Consistent Image Sizes**: Use images with similar aspect ratios for better table appearance
2. **Optimize Images**: Use appropriately sized images (thumbnails) to improve loading performance
3. **Fallback Planning**: Ensure your backend provides reliable image URLs or fallback images
4. **Alt Text**: While not directly supported, consider accessibility in your data structure
5. **CDN Usage**: Use CDN URLs for better performance and reliability

### Example from Playground
The playground demonstrates ColumnImage usage with placeholder images:

```typescript
// From playground/server/api/mock/table.get.ts
const mockData = [
  {
    id: '1',
    name: 'John Doe',
    pic: 'https://placehold.co/600x400', // ← Rendered by ColumnImage
    email: '<EMAIL>'
  }
]
```

### Technical Implementation
The component uses a computed property to extract the image URL from the `value` prop and passes it directly to the Image component with predefined styling classes.

