## Dialog (Component System)

### Overview
Dialog is a modal dialog system used by the useDialog composable to display error, info, success, warning, confirm, and loading states. It presents consistent visual styles and behavior across the app.

### Registration
- Auto-registered by @finema/core
- Used indirectly via useDialog()

### API via useDialog
- error(payload: IDialogMetaItem): Promise<boolean>
- info(payload: IDialogMetaItem): Promise<boolean>
- success(payload: IDialogMetaItem): Promise<boolean>
- warning(payload: IDialogMetaItem): Promise<boolean>
- confirm(payload: IDialogMetaItem): Promise<boolean>
- loading(payload?: Partial<IDialogMetaItem>): Promise<boolean>
- close(): void

### Types
- DialogType: 'error' | 'info' | 'success' | 'warning' | 'loading'
- IDialogMetaItem: { title: string; description?: string; icon?: string; type?: DialogType; confirmText?: string; cancelText?: string; isShowCancelBtn?: boolean; isHideIcon?: boolean; isConfirm?: boolean }

### Usage (Playground-aligned)
```vue
<script setup lang="ts">
const dialog = useDialog()

const openSuccessDialog = () => {
  dialog.success({
    title: 'Operation Successful',
    description: 'Your changes have been saved successfully.',
  })
}

const openErrorDialog = () => {
  dialog.error({
    title: 'Error Occurred',
    description: 'Unable to complete the operation. Please try again.',
    cancelText: 'Close',
  })
}

const openWarningDialog = () => {
  dialog.warning({
    title: 'Warning',
    description: 'This action cannot be undone. Are you sure you want to continue?',
    confirmText: 'Continue',
    cancelText: 'Cancel',
  }).then(() => {
    alert('success')
  }).catch(() => {
    alert('cancel')
  })
}

const openInfoDialog = () => {
  dialog.info({
    title: 'Information',
    description: 'Here is some important information you should know.',
    confirmText: 'Got it',
  })
}

const openConfirmDialog = () => {
  dialog.confirm({
    title: 'Confirm',
    description: 'Here is some important information you should know.',
  })
}

const openLoadingDialog = async () => {
  dialog.loading({ title: 'กรุณารอสักครู่...', description: 'กำลังโหลดข้อมูล...' })
  setTimeout(() => dialog.close(), 2000)
}
</script>

<template>
  <div class="space-y-4">
    <Button color="success" @click="openSuccessDialog">Open Success Dialog</Button>
    <Button color="error" @click="openErrorDialog">Open Error Dialog</Button>
    <Button color="warning" @click="openWarningDialog">Open Warning Dialog</Button>
    <Button color="info" @click="openInfoDialog">Open Info Dialog</Button>
    <Button color="info" @click="openConfirmDialog">Open Confirm Dialog</Button>
    <Button color="info" @click="openLoadingDialog">Open Loading Dialog</Button>
  </div>
</template>
```

### Theming
- Icons and colors integrate with appConfig.ui.icons and @nuxt/ui theme tokens

### Notes
- confirm resolves when confirmed and rejects on cancel for ergonomic try/catch handling

