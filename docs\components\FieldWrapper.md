## FieldWrapper (Component)

### Overview
A thin wrapper around @nuxt/ui FormField that binds common field props and slots for label/description/error. Used internally by input components via useFieldHOC.

### Props
- Inherits IFieldProps & { containerUi?: any }
  - name, label, description, hint, help, errorMessage, required, ui

### Usage
```vue
<Form>
  <FieldWrapper name="email" label="Email" :required="true">
    <UInput name="email" type="email" />
  </FieldWrapper>
</Form>
```

### Notes
- ui/containerUi allow customizing wrapper and label/field layout

