## FlexDeck (Component)

### Overview
Responsive deck/grid list component with pagination or infinite scroll. Works with useFlexDeck options and the same repo contract used by tables.

### Usage (Playground-aligned)
```vue
<script setup lang="ts">
import { onMounted, StringHelper } from '#imports'
import { type ITableItem, useTableStore, useFlexDeckStore } from '~/loaders/useTableLoader'

const tableStore = useTableStore()
const flexDeckStore = useFlexDeckStore()

tableStore.fetchSetLoading()
flexDeckStore.fetchSetLoading()

onMounted(() => {
  tableStore.fetchPage()
  flexDeckStore.fetchPage()
})

const tableOptions = useFlexDeck<ITableItem>({ repo: tableStore, options: { isEnabledSearch: true } })
const tableInfiniteOptions = useFlexDeck<ITableItem>({ repo: flexDeckStore, options: { isEnableInfiniteScroll: true } })
</script>

<template>
  <FlexDeck
    :options="tableOptions"
    container-class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"
    @pageChange="tableStore.fetchPageChange"
    @search="tableStore.fetchSearch"
  >
    <template #loading-state>
      <div class="flex h-60 items-center justify-center">
        <Icon name="i-svg-spinners:180-ring-with-bg" class="text-primary size-8" />
      </div>
    </template>

    <template #empty-state>
      <div class="min-h-60">
        <p class="text-center text-sm italic">No items found</p>
      </div>
    </template>

    <template #default="{ row }">
      <div class="rounded-xl border bg-white p-3 shadow transition hover:shadow-md">
        <img :src="row.pic" :alt="row.name" class="mb-3 w-full rounded" />
        <h3 class="font-medium">{{ row.name }}</h3>
        <p class="truncate text-sm text-gray-600">{{ StringHelper.truncate(row.email, 50) }}</p>
      </div>
    </template>
  </FlexDeck>
</template>
```

### Notes
- Slots: default (item), loading-state, empty-state
- Emits: pageChange, search
- Pair with the useFlexDeck composable

