## Form (Component)

### Overview
Form is a lightweight wrapper that renders a native <form> element and is designed to be used together with FormFields and vee-validate. Submit handling is typically provided by the form returned from useForm.

### Registration
- Auto-registered by @finema/core
- Supports optional prefix via nuxt.config.ts core.prefix (e.g., <F-Form>)

### Props
- None (serves as a structural wrapper)

### Events
- Native submit event bubbles; typically use the handler provided by vee-validate’s form.handleSubmit

### Usage (Playground-aligned)
```vue
<script setup lang="ts">
// See playground/pages/form.vue for a comprehensive demo
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.optional(v.string()),
      content: v.optional(v.string()),
      number: v.pipe(v.unknown(), v.transform((v) => Number(v))),
      mask: v.nullish(v.pipe(v.string(), v.regex(/^\d{3}-\d{3}-\d{4}$/, 'Invalid phone number format. Expected: ###-###-####'))),
      desc: v.nullish(v.pipe(v.string(), v.maxLength(5, 'Description is too long'))),
      datetime: v.nullish(v.union([v.date(), v.string()])),
      daterange: v.nullish(v.object({ start: v.union([v.date(), v.string()]), end: v.union([v.date(), v.string()]) })),
      fileUploadAuto: v.nullish(v.object({ url: v.string(), name: v.string(), path: v.string(), size: v.number() })),
      fileUpload: v.file(),
      time: v.nullish(v.string()),
    })
  ),
  initialValues: {
    name: 'John Doe',
    number: 42,
    datetime: '2025-01-15',
    daterange: { start: '2025-01-01', end: '2025-01-31' },
    content: '<p>test jaa</p>',
    time: '14:33',
  },
})

const basicInputFields = createFormFields(() => [
  { type: INPUT_TYPES.TEXT, props: { label: 'name', name: 'name', help: 'help me', description: 'description', suggestions: ['John Doe', 'Jane Smith'] } },
  { type: INPUT_TYPES.NUMBER, props: { label: 'number', name: 'number', min: 0, max: 1000 } },
  { type: INPUT_TYPES.TIME, props: { label: 'time', name: 'time' } },
  { type: INPUT_TYPES.TEXTAREA, props: { label: 'desc', name: 'desc' } },
  { type: INPUT_TYPES.TEXT, props: { label: 'mask', name: 'mask', mask: '###-###-####', placeholder: '080-568-1438' } },
])

const selectionInputFields = createFormFields(() => [
  { type: INPUT_TYPES.TOGGLE, props: { label: 'toggle', name: 'toggle', description: 'toggle' } },
  { type: INPUT_TYPES.CHECKBOX, props: { label: 'check', name: 'check', description: 'check' } },
  { type: INPUT_TYPES.SELECT, props: { label: 'select', name: 'select', description: 'select', clearable: true, options: [] } },
])

const onSubmit = form.handleSubmit((values) => {
  console.log(values)
}, moveToError)
</script>

<template>
  <Form class="mt-8 w-full" @submit="onSubmit">
    <h2 class="mt-6 mb-3 border-b pb-2 text-xl font-semibold">Basic Inputs</h2>
    <FormFields :options="basicInputFields" />

    <h2 class="mt-8 mb-3 border-b pb-2 text-xl font-semibold">Selection Inputs</h2>
    <FormFields :options="selectionInputFields" />

    <div class="space-x-4">
      <Button type="submit" class="mt-6">Submit</Button>
      <Button color="error" variant="outline" class="mt-6">Button</Button>
    </div>
  </Form>
</template>
```

### Theming
- The Form wrapper is styled via the underlying component library and the global theme (appConfig.ui).

### Related
- FormFields (component)
- useForm (composable)
- IFormField, INPUT_TYPES (types)

### Notes
- Pair Form with FormFields for dynamic field rendering and consistent layout.

