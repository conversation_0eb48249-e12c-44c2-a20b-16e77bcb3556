## FormFields (Component)

### Overview
FormFields dynamically renders a list of form input components based on an array of IFormField definitions. It integrates tightly with vee-validate and <PERSON><PERSON>’s theme system.

### Registration
- Auto-registered by @finema/core
- Optional prefix via core.prefix (e.g., <F-FormFields>)

### Props
- form?: FormContext — vee-validate form instance (optional)
- options: IFormField[] — list of field definitions
- orientation?: 'horizontal' | 'vertical' (default: 'vertical')
- class?: any — additional classes for wrapper
- ui?: typeof formTheme['slots'] — theme overrides

### Events
- Pass-through of per-field events via the `on` object in each IFormField

### Field Mapping (selected)
- INPUT_TYPES.TEXT → InputText
- INPUT_TYPES.TEXTAREA → InputTextarea
- INPUT_TYPES.SEARCH → InputSearch
- INPUT_TYPES.NUMBER → InputNumber
- INPUT_TYPES.TOGGLE → InputToggle
- INPUT_TYPES.CHECKBOX → InputCheckbox
- INPUT_TYPES.SELECT → InputSelect
- INPUT_TYPES.SELECT_MULTIPLE → InputSelectMultiple
- INPUT_TYPES.RADIO → InputRadio
- INPUT_TYPES.DATE_TIME → InputDateTime
- INPUT_TYPES.TIME → InputTime
- INPUT_TYPES.DATE_RANGE / DATE_TIME_RANGE → InputDateTimeRange
- INPUT_TYPES.UPLOAD_DROPZONE → InputUploadDropzone
- INPUT_TYPES.UPLOAD_DROPZONE_AUTO → InputUploadDropzoneAuto
- INPUT_TYPES.WYSIWYG → InputWYSIWYG
- INPUT_TYPES.COMPONENT → uses custom `option.component`

(Note: Some INPUT_TYPES may be undefined in the map and are reserved for future or specialized components.)

### Usage
```vue
<script setup lang="ts">
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.optional(v.string()),
      number: v.pipe(v.unknown(), v.transform((x) => Number(x))),
    })
  ),
})

const fields = createFormFields(() => [
  { type: INPUT_TYPES.TEXT, props: { label: 'Name', name: 'name' } },
  { type: INPUT_TYPES.NUMBER, props: { label: 'Number', name: 'number' } },
])
</script>

<template>
  <Form>
    <FormFields :form="form" :options="fields" />
    <Button type="submit">Submit</Button>
  </Form>
</template>
```

### Theming
- Uses formTheme and useUiConfig internally
- orientation: 'horizontal' injects wrapper/container classes for label and field layout

### Types
- IFormField, INPUT_TYPES, IFieldProps

### Tips
- Use createFormFields(() => [...]) to keep options reactive
- Supply per-field `on` to listen to input-specific events (e.g., search, change)

