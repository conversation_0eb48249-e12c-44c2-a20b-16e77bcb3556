## InputDateTimeRange (Form Input)

### Overview
Date/time range picker with optional multi-calendar disable.

### Props (selected)
- isDisabledMultiCalendar?: boolean
- Inherits InputDateTime props (min/max date and time options)

### Events
- change(value: { start: Date|string, end: Date|string })

### Usage
```vue
<FormFields :options="[
  { type: INPUT_TYPES.DATE_RANGE, props: { name: 'daterange', label: 'Date Range' } },
]" />
```

