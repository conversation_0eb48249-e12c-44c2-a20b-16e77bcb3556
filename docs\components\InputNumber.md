## InputNumber (Form Input)

### Overview
Numeric input with optional controls and min/max/step.

### Props (selected)
- orientation, incrementDisabled, decrementDisabled, min, max, step, disableWheelChange, formatOptions

### Events
- change(value: string)

### Usage
```vue
<FormFields :options="[
  { type: INPUT_TYPES.NUMBER, props: { name: 'number', label: 'Number', min: 0, max: 1000 } },
]" />
```

