## InputRadio (Form Input)

### Overview
Radio group with variants (list/card/table), orientation, and indicator placement.

### Props (selected)
- options: { value, label, description?, disabled?, class? }[]
- variant?: 'list' | 'card' | 'table'
- orientation?: 'horizontal' | 'vertical'
- indicator?: 'start' | 'end' | 'hidden'
- legend?: string

### Events
- change(value: any)

### Usage
```vue
<FormFields :options="[
  { type: INPUT_TYPES.RADIO, props: { name: 'theme', label: 'Theme', options: [
    { value: 'light', label: 'Light', description: 'Light theme' },
    { value: 'dark', label: 'Dark', description: 'Dark theme' },
  ] } }
]" />
```

