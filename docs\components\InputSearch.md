## InputSearch (Form Input)

### Overview
Search input with debounced events and clear handling.

### Props (selected)
- clearable?: boolean
- searchIcon?: string, clearIcon?: string

### Events
- search(value: string)
- clear()
- change(value: string)

### Usage
```vue
<FormFields :options="[
  { type: INPUT_TYPES.SEARCH, props: { name: 'search', label: 'Search', clearable: true }, on: { search: (q) => console.log(q) } },
]" />
```

