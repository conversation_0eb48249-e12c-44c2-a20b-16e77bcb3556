## InputSelect (Form Input)

### Overview
Single-select with search, icons, clearable behavior, and rich options.

### Props (selected)
- options: SelectOption[] (value/label and UI extensions)
- clearable?, loading?, searchInput?: { placeholder?, icon? }
- icon?, trailingIcon?, clearIcon?, selectedIcon?

### Events
- change(value: string)
- search(value: string)

### Usage
```vue
<FormFields :options="[
  { type: INPUT_TYPES.SELECT, props: { name: 'select', label: 'select', options: [ObjectHelper.createOption('a', 'A')] } },
]" />
```

