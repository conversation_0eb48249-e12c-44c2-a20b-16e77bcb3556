## InputSelectMultiple (Form Input)

### Overview
Multi-select with search and selected icons.

### Props (selected)
- options: SelectOption[]
- loading?, searchInput?

### Events
- change(value: string)
- search(value: string)

### Usage
```vue
<FormFields :options="[
  { type: INPUT_TYPES.SELECT_MULTIPLE, props: { name: 'selectmulti', label: 'selectmulti', options: [] }, on: { search: (q) => console.log(q) } },
]" />
```

