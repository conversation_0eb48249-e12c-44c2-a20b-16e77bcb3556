## InputText (Form Input)

### Overview
Text input with optional masking and suggestions. Supports types: text, password, email.

### Props (selected)
- name, label, description, placeholder, required, disabled, readonly, ui
- type?: 'text' | 'password' | 'email'
- mask?: MaskType, maskOptions?, maskTokens?, maskEager?, maskTokensReplace?, maskReversed?
- suggestions?: string[]

### Events
- change(value: string)
- selected(value: string)

### Usage
```vue
<FormFields :options="[
  { type: INPUT_TYPES.TEXT, props: { name: 'name', label: 'Name', suggestions: ['<PERSON>', '<PERSON>'] } },
]" />
```

