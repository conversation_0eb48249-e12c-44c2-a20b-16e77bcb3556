## InputUploadDropzone (Form Input)

### Overview
Dropzone for selecting a single file (no network request). Emits change/delete, validates type/size.

### Props (selected)
- accept?: string[] | string
- maxSize?: number (KB)
- selectFileLabel?, selectFileSubLabel?

### Events
- change(file?: File)
- delete()

### Usage
```vue
<FormFields :options="[
  { type: INPUT_TYPES.UPLOAD_DROPZONE, props: { name: 'fileUpload', label: 'File Upload', accept: '.jpg,.png,application/pdf, video/*', maxSize: 5120 } },
]" />
```

