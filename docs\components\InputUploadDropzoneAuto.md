## InputUploadDropzoneAuto (Form Input)

### Overview
Dropzone that uploads automatically using useUploadLoader and Axios config.

### Props (selected)
- requestOptions: Omit<AxiosRequestConfig, 'baseURL'> & { baseURL: string }
- uploadPathURL?: string
- bodyKey?: string (default 'file')
- responseURL?: string (default 'url')
- responsePath?: string (default 'path')
- responseName?: string (default 'file_name')
- responseSize?: string (default 'size')
- accept?: string[] | string
- maxSize?: number (KB)
- selectFileLabel?, selectFileSubLabel?, uploadingLabel?, uploadFailedLabel?, retryLabel?

### Events
- change(file?: File)
- success(res: any)
- delete()

### Usage (Playground-aligned)
```vue
<FormFields :options="[
  { type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO, props: {
    name: 'fileUploadAuto', label: 'File Upload (Auto)', placeholder: 'doc, docx, pdf (max. 5MB)',
    requestOptions: useRequestOptions().getMock(), uploadPathURL: '/upload', accept: '.jpg,.png,application/pdf, video/*', maxSize: 5120,
  }, on: { change: (f) => console.log(f), success: (r) => console.log('Upload successful:', r), delete: () => console.log('File deleted') } },
]" />
```

