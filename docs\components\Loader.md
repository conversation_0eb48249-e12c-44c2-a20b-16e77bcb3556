## Loader (Component)

### Overview
Displays a spinner or a custom loading slot while `loading` is true; otherwise renders default slot.

### Props
- loading?: boolean = true
- icon?: string = 'i-svg-spinners:180-ring-with-bg'
- ui?: typeof loaderTheme['slots']
- class?: any

### Slots
- loading: custom loading content
- default: content when not loading

### Usage
```vue
<Loader :loading="isLoading">
  <template #loading>
    <div class="p-8 text-center">Loading...</div>
  </template>
  <div>Data content</div>
</Loader>
```

### Notes
- Theming via useUiConfig(loaderTheme, 'loader')

