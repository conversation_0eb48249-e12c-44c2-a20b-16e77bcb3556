## Log (Component Suite)

### Overview
Dev-only logging utilities that teleport logs to a dedicated area.

### Components
- Log (index.vue) — renders single `data` or multiple `dataItems`
- LogItem — collapsible JSON block with syntax highlighting

### Props (Log)
- data?: any
- dataItems?: any[]
- title?: string

### Usage
```vue
<Log title="Form Values" :data-items="[form.values, post.status, post.data, post.options]" />
```

### Notes
- Teleports to #dev-logs via TeleportSafe
- DevOnly wrapper prevents rendering in production

