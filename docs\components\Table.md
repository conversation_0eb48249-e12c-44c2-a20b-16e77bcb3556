## Table (Component Suite)

### Overview
The Table system provides data display with sorting/search/pagination via a composable-driven API. It includes base/simple variants and column renderers.

### Registration
- Auto-registered by @finema/core
- Optional prefix via core.prefix (e.g., <F-Table>)

### Components
- Table (index.vue)
- Table/Base, Table/Simple
- Columns: ColumnText, ColumnNumber, ColumnImage, ColumnDate, ColumnDateTime

### Usage (Playground-style with useTable)
```vue
<script setup lang="ts">
import { COLUMN_TYPES } from '#core/components/Table/types'

// Example repo/store with fetchPage, fetchPageChange, fetchSearch, etc.
const store = useTableStore() // your implementation

const tableOptions = useTable<{ id: number; name: string; email: string; pic?: string; salary?: number; createdAt?: string }>({
  repo: store,
  options: {
    isEnabledSearch: true,
    isRouteChange: false,
  },
  columns: () => [
    { accessorKey: 'name', header: 'name' },
    { accessorKey: 'email', header: 'email', type: COLUMN_TYPES.TEXT, meta: { max: 20 } },
    { accessorKey: 'pic', header: 'pic', type: COLUMN_TYPES.IMAGE },
    { accessorKey: 'salary', header: 'salary', type: COLUMN_TYPES.NUMBER, meta: { class: { td: 'text-right', th: 'text-right' } } },
    { accessorKey: 'createdAt', header: 'createdAt', type: COLUMN_TYPES.DATE_TIME },
  ],
})

store.fetchSetLoading()
onMounted(() => {
  store.fetchPage()
})
</script>

<template>
  <Table :options="tableOptions" @pageChange="store.fetchPageChange" @search="store.fetchSearch" />
</template>
```

### Theming
- Styled via appConfig.ui.table and related theme slots

### Related
- useTable composable (state & configuration)
- Data loaders (usePageLoader, useListLoader, useObjectLoader)

