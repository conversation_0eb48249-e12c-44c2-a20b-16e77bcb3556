## Table/Base (Component)

### Overview
Low-level table renderer used by Table to render rows with typed column components and optional caption/pagination.

### Props
- status: ITableOptions['status']
- pageOptions?: ITableOptions['pageOptions']
- columns: ITableOptions['columns'] (TableColumn[])
- rawData: ITableOptions['rawData']
- isHidePagination?: boolean
- isHideCaption?: boolean

### Slots
- loading-state, empty-state, plus passthrough to underlying UTable slots

### Usage
```vue
<TableBase
  :status="options.status"
  :page-options="options.pageOptions"
  :columns="options.columns"
  :raw-data="options.rawData"
  @pageChange="onPageChange"
/>
```

