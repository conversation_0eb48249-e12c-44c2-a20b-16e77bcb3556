## useApp (Composable)

### Overview
Pinia store for page metadata and sidebar navigation used across the app.

### State
- pageMeta: { title?, sub_title?, isHideBreadcrumbs?, breadcrumbs? }
- sidebar: NavigationMenuItem[]

### Actions
- definePage(pageMeta)
- defineSidebar(items)
- patchTitle({ title?, sub_title? })
- patchBreadcrumbs({ breadcrumbs?, isHideBreadcrumbs? })

### Usage
```ts
const app = useApp()
app.definePage({ title: 'Dashboard', breadcrumbs: [{ label: 'Home', to: '/' }] })
```

