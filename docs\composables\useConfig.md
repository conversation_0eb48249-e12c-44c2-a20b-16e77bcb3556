## useCoreConfig / useUiConfig / useUiIconConfig / useUiStaticConfig

### Overview
Helpers to read core app configuration and theme tokens.

### Signatures
```ts
export const useCoreConfig: () => typeof import('../../src/core.config').core
export const useUiConfig: (config: object, name: string) => any // tailwind-variants factory
export const useUiIconConfig: (name: string) => any // returns component icon tokens
export const useUiStaticConfig: (name: string) => any // returns component slot tokens
```

### Usage
```ts
const core = useCoreConfig() // { limit_per_page, time_zone, ... }
const formTv = useUiConfig(formTheme, 'form')
const uploadIcons = useUiIconConfig('uploadFileDropzone')
```

### Notes
- appConfig values are seeded by the module and can be overridden in app.config.ts

