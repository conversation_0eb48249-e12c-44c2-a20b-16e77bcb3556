## useDialog (Composable)

### Overview
Provides a simple, promise-based API to open modal dialogs for info, success, error, warning, confirmation, and loading states.

### Signature
```ts
const dialog = useDialog()
// Returns: {
//   error, info, success, warning, confirm, loading, close
// }
```

### Types
```ts
enum DialogType { ERROR='error', INFO='info', SUCCESS='success', WARNING='warning', LOADING='loading' }
interface IDialogMetaItem {
  title: string
  description?: string
  icon?: string
  type?: DialogType
  confirmText?: string
  cancelText?: string
  isShowCancelBtn?: boolean
  isHideIcon?: boolean
  isConfirm?: boolean
}
```

### Usage (Playground-aligned)
```vue
<script setup lang="ts">
const dialog = useDialog()

const openSuccessDialog = () => {
  dialog.success({ title: 'Operation Successful', description: 'Your changes have been saved successfully.' })
}

const openErrorDialog = () => {
  dialog.error({ title: 'Error Occurred', description: 'Unable to complete the operation. Please try again.', cancelText: 'Close' })
}

const openWarningDialog = () => {
  dialog.warning({ title: 'Warning', description: 'This action cannot be undone. Are you sure you want to continue?', confirmText: 'Continue', cancelText: 'Cancel' })
    .then(() => alert('success'))
    .catch(() => alert('cancel'))
}

const openInfoDialog = () => {
  dialog.info({ title: 'Information', description: 'Here is some important information you should know.', confirmText: 'Got it' })
}

const openConfirmDialog = () => {
  dialog.confirm({ title: 'Confirm', description: 'Here is some important information you should know.' })
}

const openLoadingDialog = async () => {
  dialog.loading({ title: 'กรุณารอสักครู่...', description: 'กำลังโหลดข้อมูล...' })
  setTimeout(() => dialog.close(), 2000)
}
</script>

<template>
  <div class="space-y-4">
    <Button color="success" @click="openSuccessDialog">Open Success Dialog</Button>
    <Button color="error" @click="openErrorDialog">Open Error Dialog</Button>
    <Button color="warning" @click="openWarningDialog">Open Warning Dialog</Button>
    <Button color="info" @click="openInfoDialog">Open Info Dialog</Button>
    <Button color="info" @click="openConfirmDialog">Open Confirm Dialog</Button>
    <Button color="info" @click="openLoadingDialog">Open Loading Dialog</Button>
  </div>
</template>
```

### Notes & Gotchas
- confirm() resolves true on confirm and rejects on cancel; use try/catch
- loading() opens a persistent modal; call close() to dismiss

