## useForm (Composable) — Helpers

### Overview
In addition to vee-validate’s useForm, @finema/core provides helpers for building forms:
- useFieldHOC: wraps vee-validate useField with Finema-aware wrapper props
- createFormFields: computed helper for reactive IFormField arrays
- moveToError: scroll/focus to the first field with validation error

### Signatures
```ts
export const useFieldHOC = <TValue = unknown>(
  newFormProps: IFieldProps,
  opts?: Partial<FieldOptions<TValue>>,
): IFieldContext<TValue>

export const createFormFields: (fields: () => IFormField[]) => ComputedRef<IFormField[]>

export const moveToError: ({ errors }: { errors: Record<string, string> }) => void
```

### Usage
```vue
<script setup lang="ts">
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.string(),
    })
  ),
  initialValues: { name: '' },
})

const fields = createFormFields(() => [
  { type: INPUT_TYPES.TEXT, props: { label: 'Name', name: 'name' } },
])

const onSubmit = form.handleSubmit((values) => {
  console.log(values)
}, moveToError)
</script>

<template>
  <Form @submit="onSubmit">
    <FormFields :form="form" :options="fields" />
    <Button type="submit">Submit</Button>
  </Form>
</template>
```

### Notes
- useFieldHOC computes wrapperProps (label, placeholder, container UI, and errorMessage binding)
- moveToError focuses and scrolls smoothly to the first errored field

