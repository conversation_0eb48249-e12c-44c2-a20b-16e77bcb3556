## useListLoader (Composable)

### Overview
Fetches a non-paginated list with status + options, and utilities to clear/set loading.

### Signature
```ts
export const useListLoader = <T, O = Record<string, any>>(
  loaderOptions: IListLoaderOptions<T, O>,
) => IUseListLoader<T, O>
```

### Usage
```ts
const list = useListLoader<{ id: number; name: string }>({
  method: 'get',
  url: '/items',
  getRequestOptions: () => ({ baseURL: 'https://api.example.com' })
})

list.setLoading()
await list.run({ params: { q: 'search' } })

list.items.value // data
list.status.value // IStatus
```

### Notes
- run() accepts additional options and merges into loaderOptions
- clear(), setItems(), setLoading() helpers included

