## useNotification (Composable)

### Overview
Thin wrapper around @nuxt/ui's useToast for consistent notifications.

### Signature
```ts
const notification = useNotification()
// Returns: { info, success, warning, error, remove }
```

### Methods
- info(notification: Partial<Toast>): void
- success(notification: Partial<Toast>): void
- warning(notification: Partial<Toast>): void
- error(notification: Partial<Toast>): void
- remove: (id?: string) => void

### Usage (Playground-aligned)
```vue
<script setup lang="ts">
const notification = useNotification()

const showNotification = (type: 'info' | 'warning' | 'success' | 'error') => {
  const titles = { info: 'Information', warning: 'Warning', success: 'Success', error: 'Error' }
  notification[type]({ title: titles[type], description: `This is a ${type} notification`, duration: 5000 })
}

const showCustomNotification = (type: 'info' | 'warning' | 'success' | 'error', options: Record<string, any>) => {
  notification[type]({ title: 'Custom Notification', description: 'Notification with custom options', ...options })
}

const showActionNotification = () => {
  notification.info({
    title: 'Action Required',
    description: 'Click the button to perform an action',
    duration: 8000,
    actions: [
      {
        label: 'Action',
        onClick: () => notification.success({ title: 'Action Completed', description: 'You clicked the action button!', duration: 3000 }),
      },
    ],
  })
}
</script>

<template>
  <div class="flex max-w-[180px] flex-col space-y-4">
    <Button label="Show Info" color="info" @click="showNotification('info')" />
    <Button label="Show Warning" color="warning" @click="showNotification('warning')" />
    <Button label="Show Success" color="success" @click="showNotification('success')" />
    <Button label="Show Error" color="error" @click="showNotification('error')" />
    <Button label="Short Timeout" color="primary" @click="showCustomNotification('info', { duration: 500 })" />
    <Button label="Custom Icon" color="primary" @click="showCustomNotification('success', { icon: 'i-heroicons-star' })" />
    <Button label="With Action" color="primary" @click="showActionNotification" />
  </div>
</template>
```

### Notes
- Uses #ui/composables/useToast under the hood
- Icons: ph:info, ph:check-circle, ph:warning, ph:x-circle

