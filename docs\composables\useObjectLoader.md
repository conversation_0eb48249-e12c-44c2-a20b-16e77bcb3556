## useObjectLoader (Composable)

### Overview
Fetches/mutates a single object with status + options. Useful for detail views or submit flows.

### Signature
```ts
export const useObjectLoader = <T, B = any, O = Record<string, any>>(
  loaderOptions: IObjectLoaderOptions<T, B, O>,
) => IUseObjectLoader<T, B, O>
```

### Usage
```ts
const profile = useObjectLoader<{ id: number; name: string }, any>({
  method: 'get',
  url: '/profile',
  getRequestOptions: () => ({ baseURL: 'https://api.example.com' })
})

profile.setLoading()
await profile.run()

profile.data.value // T | null
profile.status.value // IStatus
```

### Notes
- run(payload) can send data (e.g., POST/PUT) depending on method & loader options
- clear(), setData(), setLoading() helpers included

