## usePageLoader (Composable)

### Overview
Full-featured paginated CRUD loader: fetch, find, add, update, delete with status and pageOptions, matching the Table/FlexDeck repos used in the playground.

### Key Methods & State
- fetch: { status, items, options }
- find: { status, item, options }
- add: { status, item, options }
- update: { status, item, options }
- delete: { status, item, options }
- fetchPage(page, query, opts)
- fetchSearch(query, opts)
- fetchPageChange(page, opts)
- fetchSetLoading()
- findSetLoading()
- findRun(id, opts)
- addRun({ data, ...opts })
- updateRun(id, { data, ...opts })
- deleteRun(id, opts)
- clearAll()

### Usage (Playground-aligned)
```ts
const repo = usePageLoader<{ id: number; name: string }>({
  request: { url: '/items', method: 'get' },
  getRequestOptions: () => ({ baseURL: 'https://api.example.com' })
})

repo.fetchSetLoading()
await repo.fetchPage(1)

// integrate with useTable/useFlexDeck
const tableOptions = useTable({ repo, columns: () => [...], options: { isEnabledSearch: true } })
```

### Notes
- Page options are initialized from appConfig.core (limit_per_page, default_primary_key)
- Errors are normalized via ObjectHelper, see utils docs

