## useTable / useTableSimple (Composable)

### Overview
Utilities to build table configuration from a data repo/store, mirroring the playground’s Table usage.

### Signatures
```ts
interface IUseTable<T> {
  repo: IUsePageLoader<T> | Store
  columns: () => TableColumn<T>[]
  options?: (() => Partial<ITableOptions<T>>) | Partial<ITableOptions<T>>
  transformItems?: (items: T[]) => T[]
}

export const useTable: <T>(options: IUseTable<T>) => ComputedRef<ITableOptions<T>>

interface IUseTableSimple<T> {
  items: () => T[]
  status?: () => IStatus
  columns: () => TableColumn<T>[]
  options?: (() => Partial<ISimpleTableOptions<T>>) | Partial<ISimpleTableOptions<T>>
}

export const useTableSimple: <T>(options: IUseTableSimple<T>) => ComputedRef<ISimpleTableOptions<T>>
```

### Usage (Playground-aligned)
```vue
<script setup lang="ts">
import { COLUMN_TYPES } from '#core/components/Table/types'

const store = useTableStore()

const tableOptions = useTable<{ id: number; name: string; email: string; pic?: string; salary?: number; createdAt?: string }>({
  repo: store,
  options: { isEnabledSearch: true, isRouteChange: false },
  columns: () => [
    { accessorKey: 'name', header: 'name' },
    { accessorKey: 'email', header: 'email', type: COLUMN_TYPES.TEXT, meta: { max: 20 } },
    { accessorKey: 'pic', header: 'pic', type: COLUMN_TYPES.IMAGE },
    { accessorKey: 'salary', header: 'salary', type: COLUMN_TYPES.NUMBER, meta: { class: { td: 'text-right', th: 'text-right' } } },
    { accessorKey: 'createdAt', header: 'createdAt', type: COLUMN_TYPES.DATE_TIME },
  ],
})

store.fetchSetLoading()
onMounted(() => store.fetchPage())
</script>

<template>
  <Table :options="tableOptions" @pageChange="store.fetchPageChange" @search="store.fetchSearch" />
</template>
```

### Notes
- useTableSimple is convenient for static arrays without a repo (items/status): returns ISimpleTableOptions
- createTableOptions(repo, columns, options, transformItems) is used internally by useTable

