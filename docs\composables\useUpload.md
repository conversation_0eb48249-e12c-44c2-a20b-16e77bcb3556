## useUploadLoader (Composable)

### Overview
Creates an object loader configured for multipart/form-data file uploads.

### Signature
```ts
interface IUploadRequest {
  requestOptions: Omit<AxiosRequestConfig, 'baseURL'> & { baseURL: string }
  pathURL?: string
}

export const useUploadLoader: (request: IUploadRequest) => IUseObjectLoader<any, any, any>
```

### Usage
```ts
const upload = useUploadLoader({
  requestOptions: { baseURL: 'https://api.example.com', headers: { Authorization: 'Bearer ...' } },
  pathURL: '/upload',
})

// then use upload.run({ data: FormData })
// or pair with InputUploadDropzoneAuto
```

### Notes
- Sets Content-Type to multipart/form-data automatically
- Combine with InputUploadDropzoneAuto for end-to-end upload UX

