## useWatchTrue / useWatchFalse / useWatchChange

### Overview
Convenience watchers that trigger callbacks only on specific boolean transitions or any change.

### Signatures
```ts
export const useWatchTrue: (source: () => boolean, cb: (v: boolean, o: boolean) => any) => void
export const useWatchFalse: (source: () => boolean, cb: (v: boolean, o: boolean) => any) => void
export const useWatchChange: (source: () => any, cb: (v: any, o: any) => any) => void
```

### Usage
```ts
useWatchTrue(() => isReady.value, () => { /* became true */ })
useWatchFalse(() => isOpen.value, () => { /* became false */ })
useWatchChange(() => form.values, (v, o) => console.log(v))
```

### Notes
- Relies on ParamHelper.isChangeWithTrue / isChangeWithFalse

