## @finema/core — Nuxt 3 Module Developer Guide

### Introduction
@finema/core is a Nuxt 3 module that delivers Finema’s enterprise UI Kit for Vue 3/Nuxt applications. It provides:
- Auto-registered Vue components (forms, tables, dialogs, loaders, etc.)
- Auto-imported composables (dialog, notifications, loaders, form helpers, uploads)
- Integrated validation (vee-validate + valibot) with Thai localization
- Theming via @nuxt/ui and Tailwind CSS presets
- Quality-of-life auto-imports for popular utilities (lodash via “_” prefix)

Use cases and benefits:
- Rapid UI development with consistent design and accessibility
- Production-ready form system with 20+ input types, validation, and upload flow
- Standardized data loading patterns and table rendering
- Pre-configured Nuxt integrations (@nuxt/ui, @pinia/nuxt, @vee-validate/nuxt)


### Installation
You can install the module from npm and register it in your Nuxt app.

- npm: `npm i @finema/core`
- yarn: `yarn add @finema/core`
- pnpm: `pnpm add @finema/core`

Requirements:
- Nuxt 3 (Vue 3, Vite)
- Node.js 22+ recommended


### Configuration
Register the module and (optionally) configure options and app config.

nuxt.config.ts:
```ts
// nuxt.config.ts
import { defineNuxtConfig } from 'nuxt/config'

export default defineNuxtConfig({
  modules: [
    '@finema/core',
  ],

  /**
   * Module options (configKey: "core")
   * Currently supported:
   * - prefix?: string  // Prefix to apply to all auto-registered components
   */
  core: {
    // prefix: 'F', // Example: <F-Form />, <F-FormFields />
  },

  /**
   * Optional: customize default app settings merged by the module
   * (These extend your existing config via defu)
   */
  runtimeConfig: {
    public: {
      // Overwrite if needed (module provides env-based defaults)
      // baseURL: 'https://example.com',
      // baseAPI: 'https://api.example.com',
    },
  },

  /**
   * Theme & system defaults (app.config.ts equivalent)
   */
  appConfig: {
    core: {
      // These are the defaults you can override (see below)
      // limit_per_page: 30,
      // default_primary_key: 'id',
      // time_format: 'HH:mm',
      // date_format: 'dd-MM-yyyy',
      // date_time_format: 'dd-MM-yyyy HH:mm',
      // date_format_system: 'yyyy-MM-dd',
      // date_time_format_system: 'yyyy-MM-dd HH:mm',
      // is_thai_year: false,
      // is_thai_month: false,
      // site_name: '',
      // color: '#3675FB',
      // time_zone: 'Asia/Bangkok',
    },

    /**
     * @nuxt/ui theme customization entrypoint
     * The module seeds ui config and theme slots for Finema components
     */
    // ui: { ... }
  },
})
```

Environment variables used by runtimeConfig (public):
- APP_BASE_URL, APP_BASE_API, APP_BASE_API_MOCK, APP_BASE_INTERNAL_API, PORT


### Usage
Once added to `modules`, all runtime components, composables, and utilities are auto-available.

- Components auto-registered from `@finema/core` runtime (optionally prefixed via `core.prefix`).
- Composables auto-imported from `@finema/core` (e.g., `useDialog`, `useNotification`, `useForm`, loaders, uploads, table, etc.).
- Validation helpers are auto-imported:
  - from `vee-validate`: useForm, useField, useFormErrors, useFormValues, useIsFormDirty, useIsFormValid, useResetForm, useSubmitForm
  - from `@vee-validate/valibot`: toTypedSchema
  - from `valibot`: wildcard namespace as `v` (e.g., `v.object`, `v.string`)
- Lodash lightweight import proxy: most lodash-es functions auto-import as `_name` (e.g., `_map`). Note: `get` and `isEmpty` are excluded; import them manually if needed.
- Global directive: `v-maska` available for masked inputs.

Example: Basic Form with Validation & Multiple Inputs
```vue
<script setup lang="ts">
// All used imports below are auto-imported by the module
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.optional(v.string()),
      number: v.pipe(v.unknown(), v.transform((x) => Number(x))),
      time: v.nullish(v.string()),
      desc: v.nullish(v.pipe(v.string(), v.maxLength(120))),
    })
  ),
  initialValues: {
    name: 'John Doe',
    number: 42,
    time: '14:33',
  },
})

const onSubmit = form.handleSubmit((values) => {
  console.log(values)
}, moveToError)
</script>

<template>
  <Form class="space-y-6" @submit="onSubmit">
    <FormFields :options="[
      { type: INPUT_TYPES.TEXT, props: { label: 'Name', name: 'name' } },
      { type: INPUT_TYPES.NUMBER, props: { label: 'Number', name: 'number' } },
      { type: INPUT_TYPES.TIME, props: { label: 'Time', name: 'time' } },
      { type: INPUT_TYPES.TEXTAREA, props: { label: 'Description', name: 'desc' } },
    ]" />

    <Button type="submit">Submit</Button>
  </Form>
</template>
```

Example: Dialogs & Notifications
```vue
<script setup lang="ts">
const dialog = useDialog()
const notify = useNotification()

async function removeItem() {
  try {
    await dialog.confirm({ title: 'Delete item?', description: 'This action cannot be undone.' })
    // ...perform deletion
    notify.success({ title: 'Deleted', description: 'Item removed successfully.' })
  } catch {
    notify.info({ title: 'Cancelled' })
  }
}
</script>

<template>
  <Button color="error" @click="removeItem">Delete</Button>
</template>
```

Example: Masked Input using v-maska
```vue
<template>
  <UInput v-maska="'###-###-####'" placeholder="************" />
</template>
```


### API Reference
Module Options (nuxt.config.ts → `core`):
- prefix?: string — Prefixed component names during auto-registration. Example: `prefix: 'F'` → `<F-Form />`.

App Config (app.config.ts or `appConfig` in nuxt.config.ts → `core`):
- limit_per_page: number (default 30)
- default_primary_key: string (default "id")
- time_format: string (default "HH:mm")
- date_format: string (default "dd-MM-yyyy")
- date_time_format: string (default "dd-MM-yyyy HH:mm")
- date_format_system: string (default "yyyy-MM-dd")
- date_time_format_system: string (default "yyyy-MM-dd HH:mm")
- is_thai_year: boolean
- is_thai_month: boolean
- site_name: string
- color: string (hex)
- time_zone: string (default "Asia/Bangkok")

Auto-imported Validation Helpers:
- vee-validate: useForm, useField, useFormErrors, useFormValues, useIsFormDirty, useIsFormValid, useResetForm, useSubmitForm
- @vee-validate/valibot: toTypedSchema
- valibot: namespace `v` (e.g., `v.object`, `v.union`, `v.string`, `v.date`)

Core Composables:
- useDialog(): { error, info, success, warning, confirm, loading, close }
- useNotification(): { info, success, warning, error, remove }
- useForm utilities: useFieldHOC(newFormProps, opts), createFormFields(() => IFormField[]), moveToError({ errors })
- useUpload(), useTable(), useFlexDeck()
- Data loaders: usePageLoader(), useListLoader(), useObjectLoader()

Key Components:
- Form system: Form, FormFields, FieldWrapper, and inputs:
  - Text, Textarea, Search, Number, Toggle, Checkbox, Select, SelectMultiple,
    Radio, DateTime, Time, DateRange, DateTimeRange, WYSIWYG, UploadDropzone, UploadDropzoneAuto
- Dialog: modal dialog system
- Table: Table/Base, Table/Simple, column renderers (text, number, image, date, datetime)
- Layout: FlexDeck
- Feedback: Loader, Empty, Image, TeleportSafe, Log

Types (selected):
- INPUT_TYPES enum for form inputs
- IFormField, IFieldProps, and specific input field types per component

Lodash Auto-imports:
- Most methods proxied and auto-imported as `_method` from a lightweight runtime shim.
- Exclusions include: `get`, `isEmpty` (import these directly when needed).


### Advanced Usage
Theming via @nuxt/ui
- The module configures `appConfig.ui` and loads Finema theme slots.
- Override or extend in `app.config.ts` or `nuxt.config.ts → appConfig.ui`.

Example: app.config.ts
```ts
// app.config.ts
export default defineAppConfig({
  ui: {
    colors: {
      primary: 'main',
      // secondary, tertiary, success, info, warning, error, neutral
    },
    // Override Finema theme slots if needed
    // button: { ... }, table: { ... }, form: { ... }, icons: { ... }
  },
  core: {
    time_zone: 'Asia/Bangkok',
    date_format: 'dd-MM-yyyy',
  },
})
```

Component Prefixing
- Set `core.prefix` in `nuxt.config.ts` to namespace registered components.
- Example: `core: { prefix: 'F' }` → use `<F-Form />`, `<F-FormFields />`.

Validation with valibot + vee-validate
- Use `toTypedSchema` (auto-import) to bridge valibot schemas into vee-validate.
- Thai validation messages are set globally by the module’s runtime plugin.

Integration Notes
- The module installs: `@nuxt/ui`, `@pinia/nuxt`, `@vee-validate/nuxt` (no extra setup needed).
- Vite optimizeDeps includes valibot, axios, etc. for faster dev.

Version Notes
- Tested with @finema/core v2.20.0 (Nuxt 3).


### Troubleshooting & FAQ
Styling doesn’t apply / components look unstyled
- Ensure the module is listed in `modules` and your project runs Nuxt 3 with @nuxt/ui.
- If you override `appConfig.ui`, keep required color keys (primary, secondary, etc.).

`_get` or `_isEmpty` not found
- These lodash methods are excluded from auto-import. Import directly from `lodash-es`.

Cannot use `v` or `toTypedSchema` in templates/scripts
- Confirm the module is registered. These are auto-imported; in rare cases, restart dev server.

Date/Time or Thai locale issues
- Adjust `appConfig.core` (e.g., `time_zone`, `is_thai_year`, formats) in app config.

Duplicate @nuxt/ui configuration
- The module installs @nuxt/ui with `colorMode: false`. If you also add it manually, prefer a single source of truth and merge settings carefully.

Masking directive not working
- Use `v-maska` on input components. The directive is globally registered by the module’s plugin.

Where do components/composables come from?
- All runtime code is exposed under the module’s auto-import system; no manual imports are needed for common usage.


### License & Links
- License: MIT
- Package: https://www.npmjs.com/package/@finema/core
- Source: (your repository URL)

