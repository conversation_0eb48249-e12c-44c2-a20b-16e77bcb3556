## Common Types

### IError
```ts
interface IError { code: string; message: any }
```

### IOption
```ts
interface IOption extends Record<string, any> { value: any | null; label: string }
```

### IGetParams
```ts
interface IGetParams { params?: Record<string, any> }
```

### IAPIOptions
```ts
interface IAPIOptions { _status?: number; _timestamp?: number; request?: Partial<AxiosRequestConfig>; [key: string]: any }
```

### IPageOptions
```ts
interface IPageOptions extends IAPIOptions { currentPageCount: number; currentPage: number; totalPage: number; totalCount: number; limit: number; search?: string; primary?: string }
```

### IStatus
```ts
interface IStatus { isError: boolean; isSuccess: boolean; isLoading: boolean; isLoaded: boolean; errorData: any | null }
```

