## Form Types

### Overview
Type definitions used by the Finema Form system. These types power the dynamic FormFields renderer and ensure type-safe field definitions.

### INPUT_TYPES (enum)
Selected values:
- TEXT, SEARCH, NUMBER, TEX<PERSON>RE<PERSON>, PASSWORD, EMAIL, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>LECT, SELECT_MULTIPLE, RADI<PERSON>, CHEC<PERSON>BOX, DATE_TIME, TIME, DATE, DATE_RANGE, DATE_TIME_RANGE, UPLOAD_DROPZONE, UPLOAD_DROPZONE_AUTO, WYSIWYG, TAGS, COMPONENT

### IFieldProps (base)
```ts
interface IFieldProps {
  form?: FormContext
  name: string
  errorMessage?: string
  label?: string | any
  description?: string
  hint?: string
  rules?: any
  autoFocus?: boolean
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  help?: string
  ui?: object | any
}
```

### IFormFieldBase
```ts
interface IFormFieldBase<I extends INPUT_TYPES, P extends IFieldProps, O> {
  type: I
  component?: Component
  class?: any
  ui?: object | any
  isHide?: boolean
  props: P
  on?: O
}
```

### IFormField (union)
Includes specific field types like ITextField, ISearchField, INumberField, ITextareaField, IToggleField, ISelectField, ICheckboxField, ISelectMultipleField, IRadioField, IDateTimeField, ITimeField, IDateTimeRangeField, IUploadDropzoneField, IUploadDropzoneAutoField, IWYSIWYGField, and a generic COMPONENT entry.

### Example: Text Field Type
```ts
type ITextField = IFormFieldBase<
  INPUT_TYPES.TEXT | INPUT_TYPES.PASSWORD | INPUT_TYPES.EMAIL,
  ITextFieldProps,
  { change?: (value: string) => void; selected?: (value: string) => void }
>
```

### Tips
- Use the correct INPUT_TYPES enum value to map to the intended component in FormFields
- Extend base props when creating custom COMPONENT entries

