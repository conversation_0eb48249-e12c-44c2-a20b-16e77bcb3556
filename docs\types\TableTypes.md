## Table Types

### COLUMN_TYPES (enum)
- COMPONENT, DATE, DATE_TIME, NUMBER, IMAGE, TEXT

### TableColumn<T>
Extends @nuxt/ui TableColumn with:
- type?: COLUMN_TYPES
- component?: Component
- accessorKey: string
- meta?: NuxtUiTableColumn['meta'] & Record<string, any>

### IBaseTableOptions<T>
- rawData: T[]
- status: IStatus
- columns: TableColumn<T>[]
- isHidePagination?: boolean
- isHideCaption?: boolean

### ITableOptions<T> extends IBaseTableOptions
- pageOptions: IPageOptions
- isHideToolbar?: boolean
- isEnabledSearch?: boolean
- searchPlaceholder?: string
- isRouteChange: boolean

### ISimpleTableOptions<T> extends IBaseTableOptions
- limit?: number
- primary?: number

