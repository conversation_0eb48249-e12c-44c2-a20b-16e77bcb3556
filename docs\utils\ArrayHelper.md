## ArrayHelper

### Methods
- toOptions(data: any[], valueAttr = 'id', labelAttr = 'name'): IOption[] — map array to options
- toArray<T>(value: any): Array<T | any> — normalize to array
- isEmpty(value: any): boolean — check array length === 0
- create(length: number): any[] — create array of length

### Examples
```ts
ArrayHelper.toOptions([{ id: 1, name: 'A' }]) // [{ value: 1, label: 'A' }]
ArrayHelper.isEmpty([]) // true
ArrayHelper.create(3) // [undefined, undefined, undefined]
```

