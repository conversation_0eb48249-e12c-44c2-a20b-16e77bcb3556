## NumberHelper

### Methods
- withComma(value): string — Number.toLocaleString()
- withFixed(value): string — toLocaleString with 0–2 fraction digits
- toCurrency(value, currency?): string — uses Intl.NumberFormat, 2 fraction digits, style=currency when provided
- toNumber(value): number — Number(value) || 0

### Examples
```ts
NumberHelper.withComma(1234567) // "1,234,567"
NumberHelper.withFixed(1234.5) // "1,234.5"
NumberHelper.toCurrency(50, 'USD') // "$50.00"
NumberHelper.toNumber('42') // 42
```

