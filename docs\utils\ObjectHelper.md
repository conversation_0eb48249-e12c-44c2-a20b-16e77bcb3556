## ObjectHelper

### Methods (selected)
- createOption(value, label?): IOption
- toOption(data, valueAttr = 'id', labelAttr = 'name'): IOption
- toOptions(data, valueAttr = 'id', labelAttr = 'name'): IOption[] (single item → array)
- createStatus(): IStatus
- toLoadingStatus(obj), toItemsSuccessStatus(obj, items), toObjectSuccessStatus(obj, data), toErrorStatus(obj, error), toSuccessStatus(obj), toCompleteStatus(obj)
- isInvalidParams(errorData): boolean
- isEmpty(object): boolean
- stringArrayToObject(array: string[]): Record<string, string>

### Example
```ts
const status = ObjectHelper.createStatus() // { isLoaded: false, isLoading: false, ... }
```

