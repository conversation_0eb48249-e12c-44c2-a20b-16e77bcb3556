## TimeHelper

### Overview
Date/time formatting and conversion utilities with Thai localization and timezone support.

### Key Methods
- displayDate(time): string — formats to appConfig.core.date_format
- displayDateTime(time): string — formats to appConfig.core.date_time_format
- displayDateRange(start, end): { startDate, endDate }
- toUTC(time): string — format in Zulu
- toLocal(time): string — format in configured time_zone
- getCurrentDate(customFormat?): string
- getDateFormTime(time, customFormat?): string
- getDateFormTimeWithLocal(time, customFormat?): string
- getISODateTimeFormTime(time): string
- getDateTimeFormTime(time): string
- getTimeFormTime(time): string
- getCurrentDateTime(): string

### Notes
- Honors is_thai_year and is_thai_month for Thai calendar formats

