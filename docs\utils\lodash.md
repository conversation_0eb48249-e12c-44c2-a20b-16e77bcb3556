## lodash proxy (runtime utils)

### Overview
Custom, lightweight lodash-like helpers exposed as auto-imports in the module with `_` prefix mapping. In addition, runtime includes a typed _get and _isEmpty.

### Implemented here
- _get(object, path, defaultValue?) — type-safe path resolution
- _isEmpty(value) — null/undefined, arrays, strings, objects, Map/Set

### Notes
- Many lodash-es methods are auto-imported via the module with `_method` alias, but certain exclusions apply in the module configuration. Import excluded methods directly when needed.

### Example
```ts
import { _get, _isEmpty } from '#core/utils/lodash'

const val = _get({ a: { b: 2 } }, 'a.b', 0) // 2
_isEmpty([]) // true
```

