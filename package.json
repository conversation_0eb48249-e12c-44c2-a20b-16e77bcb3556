{"name": "@finema/core", "version": "2.30.0", "repository": "https://gitlab.finema.co/finema/ui-kit", "license": "MIT", "author": "Finema Dev Core Team", "engines": {"node": ">=22"}, "type": "module", "exports": {".": {"types": "./dist/types.d.mts", "style": "./dist/runtime/styles/main.css", "default": "./dist/module.mjs"}}, "main": "./dist/module.mjs", "typesVersions": {"*": {".": ["./dist/types.d.mts"]}}, "files": ["dist"], "scripts": {"prepack": "nuxt-module-build build", "dev": "nuxi dev playground -o", "dev:build": "nuxi build playground", "dev:prepare": "nuxt-module-build build --stub && nuxt-module-build prepare && nuxi prepare playground", "release": "npm run lint && npm run test && npm run prepack && changelogen --release && npm publish && git push --follow-tags", "lint": "eslint . --quiet", "lint:fix": "eslint --fix . --quiet", "test": "vitest run", "test:watch": "vitest watch", "test:types": "vue-tsc --noEmit && cd playground && vue-tsc --noEmit", "prepare": "husky"}, "dependencies": {"@iconify-json/heroicons": "^1.2.2", "@iconify-json/ph": "^1.2.2", "@iconify-json/svg-spinners": "^1.2.2", "@nuxt/kit": "^4.0.2", "@nuxt/ui": "^3.3.2", "@pinia/nuxt": "^0.11.0", "@tailwindcss/typography": "^0.5.0-alpha.3", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/extension-youtube": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@tiptap/vue-3": "^3.0.7", "@vee-validate/nuxt": "^4.15.1", "@vee-validate/valibot": "^4.15.1", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/components": "^13.2.0", "@vueuse/core": "^13.4.0", "@wdns/vue-code-block": "^2.3.5", "axios": "^1.10.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "defu": "^6.1.4", "lodash-es": "^4.17.21", "maska": "^3.1.1", "url-join": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.26.0", "@nuxt/devtools": "^2.6.0", "@nuxt/eslint-config": "^1.4.1", "@nuxt/module-builder": "^1.0.2", "@nuxt/schema": "^4.0.2", "@nuxt/test-utils": "^3.19.0", "@types/node": "latest", "changelogen": "^0.6.1", "eslint": "^9.26.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-better-tailwindcss": "^3.3.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "nuxt": "^4.0.2", "typescript": "~5.6.3", "typescript-eslint": "^8.18.0", "vitest": "^3.1.3", "vue-tsc": "^3.0.4"}, "lint-staged": {"*": "eslint --fix --quiet"}}