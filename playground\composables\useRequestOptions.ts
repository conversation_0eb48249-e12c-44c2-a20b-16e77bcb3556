import type { AxiosRequestConfig } from 'axios'

export const useRequestOptions = () => {
  const config = useRuntimeConfig()

  const getMock = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseAPIMock || 'http://localhost:3000/api/mock',
    }
  }

  const getDefault = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseAPI,
    }
  }

  return {
    getDefault,
    getMock,
  }
}
