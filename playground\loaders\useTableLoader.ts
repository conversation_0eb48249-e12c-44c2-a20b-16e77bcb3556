import { defineStore } from 'pinia'
import { useListLoader, usePageLoader, useRequestOptions } from '#imports'

export interface ITableItem {
  id: string
  name: string
  email: string
  salary: number
  createdAt: string
  pic: string
  data: {
    value: string
  }
  createdBy: string | null
}

export const useTableStore = () => {
  const options = useRequestOptions()

  return usePageLoader<ITableItem>({
    baseURL: '/table',
    getBaseRequestOptions: options.getMock,
  })
}

export const useFlexDeckStore = defineStore('flex_deck_loader', () => {
  const options = useRequestOptions()

  return usePageLoader<ITableItem>({
    baseURL: '/flexdeck',
    getBaseRequestOptions: options.getMock,
  })
})

export const useSimpleTableStore = defineStore('simple_table_loader', () => {
  const options = useRequestOptions()

  return useListLoader<ITableItem>({
    url: '/simple-table',
    getRequestOptions: options.getMock,
  })
})

export const usePostCreate = defineStore('get', () => {
  const options = useRequestOptions()

  return useObjectLoader<ITableItem>({
    url: '/simple-table?id=:id',
    method: 'get',
    getRequestOptions: options.getMock,
  })
})
