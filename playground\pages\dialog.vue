<template>
  <div class="space-y-8">
    <!-- Introduction -->
    <div>
      <h1 class="text-2xl font-bold">
        Dialog Component
      </h1>
      <p class="mt-2 text-gray-600">
        Display modal dialogs for success, warning, error and information messages
      </p>
    </div>

    <!-- Success Dialog -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Success Dialog
      </h2>
      <div class="flex flex-wrap gap-2">
        <Button
          color="success"
          @click="openSuccessDialog"
        >
          Open Success Dialog
        </Button>
      </div>
    </div>

    <!-- Error Dialog -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Error Dialog
      </h2>
      <div class="flex flex-wrap gap-2">
        <Button
          color="error"
          @click="openErrorDialog"
        >
          Open Error Dialog
        </Button>
      </div>
    </div>

    <!-- Warning Dialog -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Warning Dialog
      </h2>
      <div class="flex flex-wrap gap-2">
        <Button
          color="warning"
          @click="openWarningDialog"
        >
          Open Warning  Dialog
        </Button>
      </div>
    </div>

    <!-- Info Dialog -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Info Dialog
      </h2>
      <div class="flex flex-wrap gap-2">
        <Button
          color="info"
          @click="openInfoDialog"
        >
          Open Info Dialog
        </Button>
      </div>
    </div>
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Confirm Dialog
      </h2>
      <div class="flex flex-wrap gap-2">
        <Button
          color="info"
          @click="openConfirmDialog"
        >
          Open Confirm Dialog
        </Button>
      </div>
    </div>
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Loading Dialog
      </h2>
      <div class="flex flex-wrap gap-2">
        <Button
          color="info"
          @click="openLoadingDialog"
        >
          Open Loading Dialog
        </Button>
      </div>
    </div>

    <!-- Custom Dialog -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Custom Dialog
      </h2>
      <div class="flex flex-wrap gap-2">
        <Button @click="openCustomDialog">
          Open Custom Dialog
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDialog } from '#imports'

definePageMeta({
  layout: 'example',
})

useSeoMeta({
  title: 'Dialog Component',
  description: 'Display modal dialogs for success, warning, error and information messages',
})

const dialog = useDialog()

// Success Dialog Example
const openSuccessDialog = () => {
  dialog.success({
    title: 'Operation Successful',
    description: 'Your changes have been saved successfully.',
  })
}

// Error Dialog Example
const openErrorDialog = () => {
  dialog.error({
    title: 'Error Occurred',
    description: 'Unable to complete the operation. Please try again.',
    cancelText: 'Close',
  })
}

// Warning Dialog Example
const openWarningDialog = () => {
  dialog.warning({
    title: 'Warning',
    description: 'This action cannot be undone. Are you sure you want to continue?',
    confirmText: 'Continue',
    cancelText: 'Cancel',
  }).then(() => {
    alert('success')
  }).catch(() => {
    alert('cancel')
  })
}

// Info Dialog Example
const openInfoDialog = () => {
  dialog.info({
    title: 'Information',
    description: 'Here is some important information you should know.',
    confirmText: 'Got it',
  })
}

const openConfirmDialog = () => {
  dialog.confirm({
    title: 'Confirm',
    description: 'Here is some important information you should know.',
  })
}

const openLoadingDialog = async () => {
  dialog.loading({
    title: 'กรุณารอสักครู่...',
    description: 'กำลังโหลดข้อมูล...',
  })

  setTimeout(() => {
    dialog.close()
  }, 2000)
}

// Custom Dialog Example
const openCustomDialog = () => {
  dialog.success({
    title: 'Custom Dialog',
    description: 'A dialog with custom configuration',
    confirmText: 'Accept',
    cancelText: 'Decline',
    isShowCancelBtn: true,
    isHideIcon: true,
  }).then(() => {
    alert('success')
  }).catch(() => {
    alert('cancel')
  })
}
</script>
