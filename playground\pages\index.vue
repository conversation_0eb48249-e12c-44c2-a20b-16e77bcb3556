<template>
  <div class="space-y-8">
    <!-- Introduction -->
    <div>
      <h1 class="text-2xl font-bold">
        @finema/core Components
      </h1>
      <p class="mt-2 text-gray-600">
        A collection of Vue components built for Nuxt 3 with Tailwind CSS
      </p>
    </div>

    <!-- Component Categories -->
    <div
      class="
        grid grid-cols-1 gap-6
        sm:grid-cols-2
        lg:grid-cols-3
      "
    >
      <!-- Forms -->
      <div class="space-y-4">
        <h2 class="text-xl font-medium">
          Form Components
        </h2>
        <div class="grid grid-cols-1 gap-3">
          <NuxtLink
            to="/form"
            class="
              flex items-center gap-3 rounded-lg border p-4 transition
              hover:bg-gray-50
            "
          >
            <Icon
              name="i-heroicons-document-text"
              class="size-5 text-gray-400"
            />
            <div>
              <p class="font-medium">Form</p>
              <p class="text-sm text-gray-600">Build complex forms with validation</p>
            </div>
          </NuxtLink>
        </div>
      </div>

      <!-- Data Display -->
      <div class="space-y-4">
        <h2 class="text-xl font-medium">
          Data Display
        </h2>
        <div class="grid grid-cols-1 gap-3">
          <NuxtLink
            to="/table"
            class="
              flex items-center gap-3 rounded-lg border p-4 transition
              hover:bg-gray-50
            "
          >
            <Icon
              name="i-heroicons-table-cells"
              class="size-5 text-gray-400"
            />
            <div>
              <p class="font-medium">Table</p>
              <p class="text-sm text-gray-600">Tabular data with sorting and pagination</p>
            </div>
          </NuxtLink>
          <NuxtLink
            to="/flexdeck"
            class="
              flex items-center gap-3 rounded-lg border p-4 transition
              hover:bg-gray-50
            "
          >
            <Icon
              name="i-heroicons-view-grid"
              class="size-5 text-gray-400"
            />
            <div>
              <p class="font-medium">FlexDeck</p>
              <p class="text-sm text-gray-600">Grid layout with pagination</p>
            </div>
          </NuxtLink>
        </div>
      </div>

      <!-- Feedback -->
      <div class="space-y-4">
        <h2 class="text-xl font-medium">
          Feedback
        </h2>
        <div class="grid grid-cols-1 gap-3">
          <NuxtLink
            to="/notification"
            class="
              flex items-center gap-3 rounded-lg border p-4 transition
              hover:bg-gray-50
            "
          >
            <Icon
              name="i-heroicons-bell-alert"
              class="size-5 text-gray-400"
            />
            <div>
              <p class="font-medium">Notification</p>
              <p class="text-sm text-gray-600">Toast notifications</p>
            </div>
          </NuxtLink>
          <NuxtLink
            to="/dialog"
            class="
              flex items-center gap-3 rounded-lg border p-4 transition
              hover:bg-gray-50
            "
          >
            <Icon
              name="i-heroicons-chat-bubble-bottom-center"
              class="size-5 text-gray-400"
            />
            <div>
              <p class="font-medium">Dialog</p>
              <p class="text-sm text-gray-600">Modal dialogs and alerts</p>
            </div>
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'example',
})

useSeoMeta({
  title: 'Finema Core Components',
})
</script>
