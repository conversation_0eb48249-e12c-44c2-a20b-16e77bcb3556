import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3'

export default defineEventHandler((event) => {
  return [
    {
      id: '5618d581-7a31-4b1d-be2b-ffcf3308c0f8',
      name: '<PERSON>',
      email: 'joh<PERSON><PERSON>@email.com',
      createdAt: '2022-05-29T19:52:32Z',
      createdBy: null,
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913be5a3ec7c',
      name: '<PERSON>',
      email: '<EMAIL>',
      createdAt: '2022-05-29T19:52:32Z',
      createdBy: 'user_uuid',
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913asda2a3ec7c',
      name: 'Ive Got A Name',
      email: '<EMAIL>',
      createdAt: '2022-05-29T19:52:32Z',
      createdBy: 'user_uuid',
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913asda2a3ec2d',
      name: '<PERSON>',
      email: '<EMAIL>',
      createdAt: '2022-05-29T19:52:32Z',
      createdBy: 'user_uuid',
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913asda2a3ec003',
      name: 'Robert Parker',
      email: '<EMAIL>',
      createdAt: '2022-05-29T19:52:32Z',
      createdBy: 'user_uuid',
    },
  ]
})
