import {
  addComponentsDir,
  addImports,
  addImportsDir,
  addPlugin,
  createResolver,
  defineNuxtModule,
  installModule,
} from '@nuxt/kit'
import defu from 'defu'
import * as lodash from 'lodash-es'
import { name, version } from '../package.json'
import { nuxtAppOptions, nuxtRunTimeConfigOptions } from './options'
import { core } from './core.config'
import * as theme from './runtime/theme'

// Module options TypeScript interface definition
export interface ModuleOptions {
  prefix?: string
}

declare module '@nuxt/schema' {
  interface AppConfigInput { core?: Partial<typeof core> }
}

export default defineNuxtModule<ModuleOptions>({
  meta: {
    name,
    version,
    configKey: 'core',
  },
  // Default configuration options of the Nuxt module
  defaults: {},
  async setup(_options, _nuxt) {
    const {
      resolve,
    } = createResolver(import.meta.url)

    // Transpile the runtime
    const runtimeDir = resolve('./runtime')

    _nuxt.options.build.transpile.push(runtimeDir)
    _nuxt.options.alias['#core'] = runtimeDir
    _nuxt.options.css.push(resolve(runtimeDir, 'styles/main.css'))

    _nuxt.options.app = defu(nuxtAppOptions as any, _nuxt.options.app)
    _nuxt.options.runtimeConfig = defu(nuxtRunTimeConfigOptions as any,
      _nuxt.options.runtimeConfig)

    _nuxt.options.appConfig.core = defu(_nuxt.options.appConfig.core || {},
      core)

    // Configure color mode through @nuxt/ui module instead
    _nuxt.options.colorMode = {
      preference: 'light',
    }

    _nuxt.options.appConfig.ui = {
      colors: {
        primary: 'main',
        secondary: 'secondary',
        tertiary: 'tertiary',
        success: 'success',
        info: 'info',
        warning: 'warning',
        error: 'error',
        neutral: 'slate',
      },
      ...theme,
    }

    _nuxt.options.build = defu(
      {
        transpile: [
          ...(_nuxt.options.build?.transpile || []),
          'date-fns',
          'date-fns-tz',
          '@vuepic/vue-datepicker',
          'defu',
          'form-data',
        ],
      },
      _nuxt.options.build,
    )

    _nuxt.options.vite = defu(
      {
        optimizeDeps: {
          include: [...(_nuxt.options.vite?.optimizeDeps?.include || []),
            '@wdns/vue-code-block',
            'valibot',
            '@vee-validate/valibot',
            'axios',
            'tailwind-variants',
            '@vueuse/core'],
        },
      },
      _nuxt.options.vite,
    )

    await installModule('@nuxt/ui', {
      prefix: '',
      colorMode: false,
      theme: {
        colors: [
          'primary',
          'secondary',
          'tertiary',
          'info',
          'success',
          'warning',
          'error'],
      },
    })

    await installModule('@pinia/nuxt')
    await installModule('@vee-validate/nuxt', {
      autoImports: false,
    },
    )

    // await installModule('nuxt-lodash', {
    //   prefix: '_',
    //   upperAfterPrefix: false,
    //   exclude: ['get'],
    // })

    const excludes = [
      'wrapperValue',
      'wrapperToIterator',
      'wrapperReverse',
      'wrapperPlant',
      'wrapperNext',
      'wrapperLodash',
      'wrapperCommit',
      'wrapperChain',
      'wrapperAt',
      'templateSettings',
      'toIterator',
      'VERSION',
      'lodash',
      'value',
      'valueOf',
      'toJSON',
      'thru',
      'plant',
      'next',
      'default',
      'commit',
      'head',
      'get',
      'isEmpty',
    ]

    for (const name of Object.keys(lodash)) {
      if (!excludes.includes(name)) {
        const alias = name
        const prefix = '_'
        const as = prefix + alias

        addImports({
          name,
          as,
          from: resolve('./runtime/lodash'),
        })
      }
    }

    // Do not add the extension since the `.ts` will be transpiled to `.mjs` after `npm run prepack`
    addPlugin(resolve('./runtime/plugin'))

    void addComponentsDir({
      path: resolve(runtimeDir, 'components'),
      prefix: _options.prefix,
      priority: 10,
    })

    // Add Composables
    addImportsDir(resolve(runtimeDir, 'composables'))
    addImportsDir(resolve(runtimeDir, 'utils'))

    // Add main types directory
    addImportsDir(resolve(runtimeDir, 'types'))

    // Add specific imports for commonly used validation functions
    addImports([
      // Vee-Validate imports
      {
        name: 'useForm',
        from: 'vee-validate',
      },
      {
        name: 'useField',
        from: 'vee-validate',
      },
      {
        name: 'useFormErrors',
        from: 'vee-validate',
      },
      {
        name: 'useFormValues',
        from: 'vee-validate',
      },
      {
        name: 'useIsFormDirty',
        from: 'vee-validate',
      },
      {
        name: 'useIsFormValid',
        from: 'vee-validate',
      },
      {
        name: 'useResetForm',
        from: 'vee-validate',
      },
      {
        name: 'useSubmitForm',
        from: 'vee-validate',
      },
      {
        name: 'toTypedSchema',
        from: '@vee-validate/valibot',
      },
      // Valibot namespace import
      {
        name: '*',
        as: 'v',
        from: 'valibot',
      },
    ])
  },
})
