<template>
  <NuxtLoadingIndicator :color="coreConfig.color" />
  <UApp
    :locale="th"
    :toaster="toaster"
  >
    <slot />
  </UApp>
  <DevOnly>
    <DevToolsWindow />
  </DevOnly>
</template>

<script lang="ts" setup>
import { th } from '@nuxt/ui/locale'
import type { ToasterProps } from '@nuxt/ui'
import UApp from '#ui/components/App'
import { useHead } from '#imports'
import { useCoreConfig } from '#core/composables/useConfig'
import DevToolsWindow from '#core/components/DevToolsWindow/index.vue'

defineProps<{
  toaster?: ToasterProps | null
}>()

const coreConfig = useCoreConfig()

useHead({
  titleTemplate: (titleChunk: string | null | undefined) => {
    return titleChunk ? `${titleChunk} - ${coreConfig.site_name}` : coreConfig.site_name
  },
})
</script>
