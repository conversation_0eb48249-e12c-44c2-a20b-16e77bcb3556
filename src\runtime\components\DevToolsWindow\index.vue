<template>
  <div
    v-show="isShowDevTools"
    ref="devToolsRef"
    class="
      fixed z-50 overflow-hidden rounded-lg border border-neutral-300 bg-white
      opacity-80 shadow-2xl
    "
    :style="devToolsDynamicStyles"
  >
    <!-- Draggable Title Bar -->
    <div class="flex items-center justify-between px-2 py-1 select-none">
      <p
        class="flex-grow cursor-move text-sm font-semibold"
        @mousedown.prevent="handleDragStart"
      >
        Debug Tools
      </p>
      <div class="flex items-center">
        <Button
          icon="i-heroicons-arrow-path"
          size="xs"
          color="neutral"
          variant="ghost"
          class="mr-1"
          title="Reset Position & Size"
          @click.stop="resetDevToolsState"
        />
        <Button
          icon="i-heroicons-x-mark"
          size="xs"
          color="neutral"
          variant="ghost"
          title="Close DevTools"
          @click.stop="closeDevTools"
        />
      </div>
    </div>
    <hr class="text-neutral-300" />
    <!-- Content Area Target for Logs -->
    <div
      id="dev-logs"
      class="flex flex-1 flex-col space-y-1 overflow-auto p-2"
      :style="{ height: `calc(${devToolsHeight} - 40px)` }"
    />

    <!-- Resize Handles -->
    <div
      v-if="!isDragging"
      class="resize-handles"
    >
      <div
        class="resize-handle top-left"
        @mousedown.prevent="handleResizeStart('top-left', $event)"
      />
      <div
        class="resize-handle top-center"
        @mousedown.prevent="handleResizeStart('top', $event)"
      />
      <div
        class="resize-handle top-right"
        @mousedown.prevent="handleResizeStart('top-right', $event)"
      />
      <div
        class="resize-handle middle-left"
        @mousedown.prevent="handleResizeStart('left', $event)"
      />
      <div
        class="resize-handle middle-right"
        @mousedown.prevent="handleResizeStart('right', $event)"
      />
      <div
        class="resize-handle bottom-left"
        @mousedown.prevent="handleResizeStart('bottom-left', $event)"
      />
      <div
        class="resize-handle bottom-center"
        @mousedown.prevent="handleResizeStart('bottom', $event)"
      />
      <div
        class="resize-handle bottom-right"
        @mousedown.prevent="handleResizeStart('bottom-right', $event)"
      />
    </div>
  </div>

  <!-- Toggle button for this DevToolsWindow -->
  <div
    class="fixed right-1 bottom-1 z-[99999]"
  >
    <Button
      :icon="isShowDevTools ? 'heroicons:x-mark' : 'heroicons:information-circle'"
      color="info"
      square
      size="sm"
      :ui="{ base: 'rounded-full' }"
      @click="toggleDevTools"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, onUnmounted, computed } from 'vue'
import { useStorage } from '@vueuse/core'

const isShowDevTools = useStorage('isShowDevTools', false)

// Define Desktop Defaults first
const DEFAULT_WIDTH = '300px'
const DEFAULT_HEIGHT = '400px'
const DEFAULT_POSITION = {
  top: 'auto',
  left: 'auto',
  bottom: '2.5rem',
  right: '0.5rem',
}

const devToolsWidth = useStorage('devToolsWidth', DEFAULT_WIDTH)
const devToolsHeight = useStorage('devToolsHeight', DEFAULT_HEIGHT)
const devToolsPosition = useStorage('devToolsPosition', JSON.parse(JSON.stringify(DEFAULT_POSITION)))

const devToolsRef = ref<HTMLElement | null>(null)

// Drag state
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartY = ref(0)
const initialDragPosition = ref({
  top: 0,
  left: 0,
})

// Resize state
const isResizing = ref(false)
const activeResizeHandle = ref<string | null>(null)
const initialResizeState = ref({
  width: 0,
  height: 0,
  top: 0,
  left: 0,
  mouseX: 0,
  mouseY: 0,
})

const MIN_WIDTH = 150
const MIN_HEIGHT = 100 // Title bar (approx 40px) + some content area

const devToolsDynamicStyles = computed(() => {
  const styles: Record<string, string> = {
    width: devToolsWidth.value,
    height: devToolsHeight.value,
    maxWidth: '100vw',
    maxHeight: '100vh',
  }

  if (devToolsPosition.value.top !== 'auto') {
    styles.top = devToolsPosition.value.top
    styles.bottom = 'auto'
  } else {
    styles.bottom = devToolsPosition.value.bottom
  }

  if (devToolsPosition.value.left !== 'auto') {
    styles.left = devToolsPosition.value.left
    styles.right = 'auto'
  } else {
    styles.right = devToolsPosition.value.right
  }

  return styles
})

const toggleDevTools = () => {
  isShowDevTools.value = !isShowDevTools.value
}

const closeDevTools = () => {
  isShowDevTools.value = false
}

// --- Drag Logic ---
const handleDragStart = (event: MouseEvent) => {
  if (!devToolsRef.value || event.button !== 0) return
  isDragging.value = true
  const rect = devToolsRef.value.getBoundingClientRect()

  initialDragPosition.value = {
    top: rect.top,
    left: rect.left,
  }

  dragStartX.value = event.clientX
  dragStartY.value = event.clientY
  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('mouseup', handleDragEnd)
}

const handleDragMove = (event: MouseEvent) => {
  if (!isDragging.value || !devToolsRef.value) return
  const deltaX = event.clientX - dragStartX.value
  const deltaY = event.clientY - dragStartY.value
  let newTop = initialDragPosition.value.top + deltaY
  let newLeft = initialDragPosition.value.left + deltaX
  const elHeight = devToolsRef.value.offsetHeight
  const elWidth = devToolsRef.value.offsetWidth

  newTop = Math.max(0, Math.min(newTop, window.innerHeight - elHeight))
  newLeft = Math.max(0, Math.min(newLeft, window.innerWidth - elWidth))
  devToolsPosition.value = {
    top: `${newTop}px`,
    left: `${newLeft}px`,
    bottom: 'auto',
    right: 'auto',
  }
}

const handleDragEnd = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
}

// --- Resize Logic ---
const handleResizeStart = (handle: string, event: MouseEvent) => {
  if (!devToolsRef.value || event.button !== 0) return
  isResizing.value = true
  activeResizeHandle.value = handle
  const rect = devToolsRef.value.getBoundingClientRect()

  initialResizeState.value = {
    width: rect.width,
    height: rect.height,
    top: rect.top,
    left: rect.left,
    mouseX: event.clientX,
    mouseY: event.clientY,
  }

  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
}

const handleResizeMove = (event: MouseEvent) => {
  if (!isResizing.value || !devToolsRef.value || !activeResizeHandle.value) return
  const {
    width, height, top, left, mouseX, mouseY,
  } = initialResizeState.value

  const deltaX = event.clientX - mouseX
  const deltaY = event.clientY - mouseY
  let newWidth = width
  let newHeight = height
  let newTop = top
  let newLeft = left

  if (activeResizeHandle.value.includes('right')) {
    newWidth = Math.max(MIN_WIDTH, width + deltaX)
  }

  if (activeResizeHandle.value.includes('bottom')) {
    newHeight = Math.max(MIN_HEIGHT, height + deltaY)
  }

  if (activeResizeHandle.value.includes('left')) {
    newWidth = Math.max(MIN_WIDTH, width - deltaX)

    if (newWidth > MIN_WIDTH) {
      newLeft = left + deltaX
    } else {
      newLeft = left + (width - MIN_WIDTH)
    }
  }

  if (activeResizeHandle.value.includes('top')) {
    newHeight = Math.max(MIN_HEIGHT, height - deltaY)

    if (newHeight > MIN_HEIGHT) {
      newTop = top + deltaY
    } else {
      newTop = top + (height - MIN_HEIGHT)
    }
  }

  if (newTop !== top || newLeft !== left) {
    devToolsPosition.value = {
      top: `${newTop}px`,
      left: `${newLeft}px`,
      bottom: 'auto',
      right: 'auto',
    }
  }

  devToolsWidth.value = `${newWidth}px`
  devToolsHeight.value = `${newHeight}px`
}

const handleResizeEnd = () => {
  isResizing.value = false
  activeResizeHandle.value = null
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
}

const resetDevToolsState = () => {
  devToolsWidth.value = DEFAULT_WIDTH
  devToolsHeight.value = DEFAULT_HEIGHT
  devToolsPosition.value = JSON.parse(JSON.stringify(DEFAULT_POSITION)) // Deep copy to ensure reactivity

  // After resetting, if the window is positioned with bottom/right,
  // ensure top/left are 'auto' for correct application of styles.
  // The computed devToolsDynamicStyles should handle this, but an explicit update might be needed
  // if Vue doesn't immediately pick up the 'auto' change for bottom/right from the new object.
  if (devToolsRef.value) {
    // Force re-application of styles if needed, especially for position
    // This is a bit of a heavy-handed way, ideally Vue's reactivity handles it.
    const newStyles = {
      width: DEFAULT_WIDTH,
      height: DEFAULT_HEIGHT,
      ...DEFAULT_POSITION,
    }

    Object.keys(newStyles).forEach((key) => {
      (devToolsRef.value as HTMLElement).style[key as any] = newStyles[key as any]
    })

    // Ensure bottom/right are applied if top/left are auto
    if (newStyles.top === 'auto') {
      (devToolsRef.value as HTMLElement).style.bottom = newStyles.bottom
    } else {
      (devToolsRef.value as HTMLElement).style.bottom = 'auto'
    }

    if (newStyles.left === 'auto') {
      (devToolsRef.value as HTMLElement).style.right = newStyles.right
    } else {
      (devToolsRef.value as HTMLElement).style.right = 'auto'
    }
  }
}

onMounted(() => {
  watch(isShowDevTools, (newValue) => {
    if (newValue && devToolsRef.value) {
      const currentStyle = devToolsDynamicStyles.value

      Object.keys(currentStyle).forEach((key) => {
        if (devToolsRef.value) {
          (devToolsRef.value as HTMLElement).style[key as any] = currentStyle[key as any]
        }
      })
    }
  }, {
    immediate: true,
  })
})

onUnmounted(() => {
  if (isDragging.value) handleDragEnd()
  if (isResizing.value) handleResizeEnd()
})
</script>

<style scoped>
.resize-handles div {
  position: absolute;
  width: 10px;
  height: 10px;
  z-index: 1; /* Above parent, but allow content to be interactive */
}
.resize-handle.top-left { top: -5px; left: -5px; cursor: nwse-resize; }
.resize-handle.top-center { top: -5px; left: 50%; transform: translateX(-50%); cursor: ns-resize; }
.resize-handle.top-right { top: -5px; right: -5px; cursor: nesw-resize; }
.resize-handle.middle-left { top: 50%; left: -5px; transform: translateY(-50%); cursor: ew-resize; }
.resize-handle.middle-right { top: 50%; right: -5px; transform: translateY(-50%); cursor: ew-resize; }
.resize-handle.bottom-left { bottom: -5px; left: -5px; cursor: nesw-resize; }
.resize-handle.bottom-center { bottom: -5px; left: 50%; transform: translateX(-50%); cursor: ns-resize; }
.resize-handle.bottom-right { bottom: -5px; right: -5px; cursor: nwse-resize; }

#dev-logs {
  overscroll-behavior: contain;
}

#dev-logs:empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; /* Ensure it takes up available space for centering */
}

#dev-logs:empty::before {
  content: "No logs to display.";
  color: #9ca3af; /* text-gray-400 */
  font-style: italic;
}
</style>
