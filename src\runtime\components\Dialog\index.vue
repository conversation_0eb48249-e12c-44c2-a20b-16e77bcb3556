<template>
  <AlertDialogRoot :open="true">
    <AlertDialogPortal>
      <AlertDialogOverlay :class="theme.overlay()" />
      <AlertDialogContent
        :class="theme.base()"
      >
        <Icon
          v-if="!propsSafe.isHideIcon"
          :name="getIcon || ''"
          :class="theme.icon()"
        />
        <div :class="theme.wrapper()">
          <AlertDialogTitle :class="theme.title()">
            {{ propsSafe.title }}
          </AlertDialogTitle>
          <AlertDialogDescription
            v-if="propsSafe.description"
            :class="theme.description()"
          >
            {{ propsSafe.description }}
          </AlertDialogDescription>
          <div
            v-if="propsSafe.type !== DialogType.LOADING"
            :class="theme.buttonGroup()"
          >
            <Button
              v-if="propsSafe.isShowCancelBtn"
              type="button"
              color="neutral"
              variant="outline"
              :class="theme.cancelButton()"
              @click="emits('close', false)"
            >
              {{ propsSafe.cancelText }}
            </Button>
            <Button
              type="button"
              :color="propsSafe.isConfirm ? staticTheme.confirmColor : propsSafe.type"
              :class="theme.confirmButton()"
              @click="emits('close', true)"
            >
              {{ propsSafe.confirmText }}
            </Button>
          </div>
        </div>
      </AlertDialogContent>
    </AlertDialogPortal>
  </AlertDialogRoot>
</template>

<script lang="tsx" setup>
import {
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogOverlay,
  AlertDialogPortal,
  AlertDialogRoot,
  AlertDialogTitle,
} from 'reka-ui'
import { computed, useAttrs } from 'vue'
import { useUiIconConfig, useUiConfig, useUiStaticConfig } from '../../composables/useConfig'
import type { IDialogMetaItem } from '#core/composables/useDialog'
import { dialogTheme } from '#core/theme/dialog'
import { DialogType } from '#core/composables/useDialog'

const emits = defineEmits<{ close: [boolean] }>()
const props = withDefaults(defineProps<IDialogMetaItem>(), {
  confirmText: 'ตกลง',
  cancelText: 'ยกเลิก',
})

const attrs = useAttrs()
const icons = useUiIconConfig('dialog')
const staticTheme = useUiStaticConfig('dialog')

const propsSafe = computed(() => {
  if (props.title) {
    return props
  }

  return {
    ...attrs,
    cancelText: attrs.cancelText || 'ยกเลิก',
    confirmText: attrs.confirmText || 'ตกลง',
  } as IDialogMetaItem
})

const getIcon = computed(() => {
  if (propsSafe.value.icon) {
    return propsSafe.value.icon
  }

  if (propsSafe.value.isConfirm) {
    return icons.iconConfirm
  }

  switch (propsSafe.value.type) {
    case DialogType.SUCCESS:
      return icons.iconSuccess
    case DialogType.ERROR:
      return icons.iconError
    case DialogType.INFO:
      return icons.iconInfo
    case DialogType.WARNING:
      return icons.iconWarning
    case DialogType.LOADING:
      return icons.iconLoading
    default:
      return icons.iconInfo
  }
})

const theme = computed(() => useUiConfig(dialogTheme, 'dialog')({
  color: propsSafe.value.isConfirm ? undefined : propsSafe.value.type,
  confirm: propsSafe.value.isConfirm,
}))
</script>
