<template>
  <div
    :class="theme.base({
      class: [ui?.base, props.class],
    })"
  >
    <Icon
      :name="icon"
      :class="theme.iconSize({
        class: [ui?.iconSize],
      })"
    />
    <p
      :class="theme.message({
        class: [ui?.message],
      })"
      v-html="message"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useUiConfig } from '#core/composables/useConfig'
import { emptyTheme } from '#core/theme/empty'

const props = withDefaults(
  defineProps<{
    message?: any
    icon?: string
    ui?: typeof emptyTheme['slots']
    class?: any
  }>(),
  {
    message: 'ไม่พบข้อมูล!',
    icon: 'ph:table-thin',
  },
)

const theme = computed(() => useUiConfig(emptyTheme, 'empty')())
</script>
