<template>
  <FieldWrapper
    v-bind="wrapperProps"
    label=""
    description=""
  >
    <Checkbox
      :model-value="value"
      :disabled="wrapperProps.disabled"
      :name="name"
      :label="label"
      :description="description"
      :required="required"
      :variant="variant"
      :indicator="indicator"
      :ui="ui"
      @update:modelValue="onChange"
    />
  </FieldWrapper>
</template>

<script lang="ts" setup>
import { useFieldHOC } from '#core/composables/useForm'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import type { ICheckboxFieldProps } from '#core/components/Form/InputCheckbox/types'

const emits = defineEmits(['change'])
const props = withDefaults(defineProps<ICheckboxFieldProps>(), {})

const {
  value, wrapperProps, handleChange,
} = useFieldHOC<boolean>(props)

const onChange = (value: any) => {
  handleChange(value)
  emits('change', value)
}
</script>
