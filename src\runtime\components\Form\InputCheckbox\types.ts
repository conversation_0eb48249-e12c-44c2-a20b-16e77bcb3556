import type {
  IFieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'

export interface ICheckboxFieldProps extends IFieldProps {
  label?: string
  description?: string
  variant?: 'card' | 'list'
  indicator?: 'start' | 'end' | 'hidden'
}

export type ICheckboxField = IFormFieldBase<
  INPUT_TYPES.CHECKBOX,
  ICheckboxFieldProps,
  {
    change?: (value: boolean) => void
  }
>
