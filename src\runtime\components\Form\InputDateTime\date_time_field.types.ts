import type {
  <PERSON>ieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'

export interface ITimeOption {
  hours?: number | string
  minutes?: number | string
  seconds?: number | string
}

export interface IDateTimeFieldProps extends IFieldProps {
  clearIcon?: string
  disabledTime?: boolean
  minDate?: Date | string
  maxDate?: Date | string
  startTime?: ITimeOption
  minTime?: ITimeOption
  maxTime?: ITimeOption
  isReturnISO?: boolean
}

export type IDateTimeField = IFormFieldBase<
  INPUT_TYPES.DATE_TIME | INPUT_TYPES.DATE,
  IDateTimeFieldProps,
  {
    change: (value: string) => void
  }
>
