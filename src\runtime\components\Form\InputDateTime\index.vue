<template>
  <FieldWrapper v-bind="wrapperProps">
    <Datepicker
      :model-value="innerValue"
      :disabled="wrapperProps.disabled"
      cancel-text="ยกเลิก"
      select-text="เลือก"
      locale="th"
      :enable-time-picker="!disabledTime"
      :placeholder="wrapperProps.placeholder"
      :format="format"
      :min-date="minDate"
      :max-date="maxDate"
      :min-time="minTime"
      :max-time="maxTime"
      :start-time="startTime"
      :required="required"
      :flow="['calendar', 'time']"
      @update:model-value="onInput"
    >
      <template
        v-if="appConfig.core?.is_thai_year"
        #year="{ value }"
      >
        {{ value + 543 }}
      </template>
      <template
        v-if="appConfig.core?.is_thai_year"
        #year-overlay-value="{ value }"
      >
        {{ value + 543 }}
      </template>
      <template #dp-input="{ value: innerValue }">
        <Input
          :trailing-icon="innerValue ? undefined : 'i-heroicons-calendar-days'"
          type="text"
          :disabled="wrapperProps.disabled"
          :model-value="innerValue"
          :placeholder="wrapperProps.placeholder"
          :readonly="true"
          :ui="{
            base: 'cursor-pointer select-none',
            trailingIcon: 'cursor-pointer',
          }"
        />
      </template>
      <template #clear-icon="{ clear }">
        <Icon
          :name="clearIcon"
          :class="theme.clearIcon({
            class: [ui?.clearIcon],
          })"
          @click.stop="clear"
        />
      </template>
    </Datepicker>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import Datepicker from '@vuepic/vue-datepicker'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { computed, TimeHelper, useAppConfig, useFieldHOC, useUiConfig, ref, watch, onMounted } from '#imports'
import '@vuepic/vue-datepicker/dist/main.css'
import type { IDateTimeFieldProps } from '#core/components/Form/InputDateTime/date_time_field.types'
import { dateTimeTheme } from '#core/theme/dateTime'

const props = withDefaults(defineProps<IDateTimeFieldProps>(), {
  clearIcon: 'ph:x-circle-fill',
})

const appConfig = useAppConfig()
const theme = computed(() => useUiConfig(dateTimeTheme, 'dateTime')())

const {
  value, wrapperProps,
} = useFieldHOC<string | Date>(props)

const innerValue = ref<Date | undefined>()

// Initialize innerValue with current form value or default value
const initializeValue = () => {
  const currentValue = value.value

  if (currentValue) {
    innerValue.value = typeof currentValue === 'string' ? new Date(currentValue) : currentValue
  } else {
    innerValue.value = undefined
  }
}

// Watch for changes in form value to sync with datepicker
watch(value, (newValue) => {
  if (newValue) {
    innerValue.value = typeof newValue === 'string' ? new Date(newValue) : newValue
  } else {
    innerValue.value = undefined
  }
}, {
  immediate: false,
})

onMounted(() => {
  initializeValue()
})

const format = (date: Date) => {
  if (props.disabledTime) {
    return TimeHelper.displayDate(date)
  }

  return TimeHelper.displayDateTime(date)
}

const onInput = (_value: any) => {
  if (props.disabledTime && !props.isReturnISO) {
    value.value = TimeHelper.getDateFormTime(_value)
  } else {
    value.value = _value
  }
}
</script>
