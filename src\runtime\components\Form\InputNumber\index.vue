<template>
  <FieldWrapper v-bind="wrapperProps">
    <InputNumber
      :model-value="value"
      :disabled="wrapperProps.disabled"
      :name="name"
      :placeholder="wrapperProps.placeholder"
      :autofocus="!!autoFocus"
      :readonly="readonly"
      :orientation="orientation"
      :increment-disabled="incrementDisabled"
      :decrement-disabled="decrementDisabled"
      :min="min"
      :max="max"
      :step="step"
      :disable-wheel-change="disableWheelChange"
      :format-options="formatOptions"
      :ui="ui"
      @update:model-value="onChange"
    />
  </FieldWrapper>
</template>

<script lang="ts" setup>
import { useFieldHOC } from '#core/composables/useForm'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import type { INumberFieldProps } from '#core/components/Form/InputNumber/types'

const emits = defineEmits(['change'])
const props = withDefaults(defineProps<INumberFieldProps>(), {
  orientation: 'vertical',
})

const {
  value, wrapperProps, handleChange,
} = useFieldHOC<number>(props)

const onChange = (value: any) => {
  handleChange(value)
  emits('change', value)
}
</script>
