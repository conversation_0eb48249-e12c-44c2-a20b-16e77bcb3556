<template>
  <FieldWrapper v-bind="wrapperProps">
    <SelectMenu
      :model-value="value"
      :items="options"
      :placeholder="wrapperProps.placeholder"
      :disabled="wrapperProps.disabled"
      :loading="loading"
      :search-input="searchInput"
      :selected-icon="selectedIcon"
      value-key="value"
      label-key="label"
      :icon="icon"
      :ui="ui"
      :leading-icon="options.find((item) => item.value === value)?.icon"
      :avatar="options.find((item) => item.value === value)?.avatar"
      @update:modelValue="onChange"
      @update:searchTerm="onSearch"
    >
      <template #default="{ modelValue }">
        <div
          v-if="value"
          :class="theme.selectedWrapper({
            class: [ui?.selectedWrapper],
          })"
        >
          <span
            :class="theme.selectedLabel({
              class: [ui?.selectedLabel],
            })"
          >
            {{ options.find((item) => item.value === modelValue)?.label || modelValue }}
          </span>
          <Icon
            v-if="clearable"
            :name="clearIcon"
            :class="theme.clearIcon({
              class: [ui?.clearIcon],
            })"
            @click.stop="onChange(undefined)"
          />
        </div>
      </template>
    </SelectMenu>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { useFieldHOC } from '#core/composables/useForm'
import type { ISelectFieldProps } from '#core/components/Form/InputSelect/types'
import { selectMenuTheme } from '#core/theme/selectMenu'
import { useUiConfig } from '#core/composables/useConfig'

const emits = defineEmits([
  'change',
  // 'search',
])

const props = withDefaults(defineProps<ISelectFieldProps>(), {
  clearIcon: 'ph:x-circle-fill',
})

const theme = computed(() => useUiConfig(selectMenuTheme, 'selectMenu')())

const {
  value, wrapperProps, handleChange,
} = useFieldHOC<any>(props)

const onChange = (value: any) => {
  handleChange(value)
  emits('change', value)
}

const onSearch = (value: any) => {
  // eslint-disable-next-line
  emits('search', value)
}
</script>
