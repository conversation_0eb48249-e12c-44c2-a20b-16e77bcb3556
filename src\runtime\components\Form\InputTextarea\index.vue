<template>
  <FieldWrapper v-bind="wrapperProps">
    <Textarea
      :model-value="value"
      :disabled="wrapperProps.disabled"
      :name="name"
      :resize="resize"
      :placeholder="wrapperProps.placeholder"
      :autofocus="!!autoFocus"
      :autoresize="autoresize"
      :rows="rows"
      :maxrows="maxrows"
      :loading="loading"
      :loading-icon="loadingIcon"
      :readonly="readonly"
      :ui="ui"
      @update:model-value="onChange"
    />
  </FieldWrapper>
</template>

<script lang="ts" setup>
import { useFieldHOC } from '#core/composables/useForm'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import type { ITextareaFieldProps } from '#core/components/Form/InputTextarea/types'

const emits = defineEmits(['change'])
const props = withDefaults(defineProps<ITextareaFieldProps>(), {})

const {
  value, wrapperProps, handleChange,
} = useFieldHOC<string>(props)

const onChange = (value: any) => {
  handleChange(value)
  emits('change', value)
}
</script>
