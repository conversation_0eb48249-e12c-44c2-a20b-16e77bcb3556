import type {
  IFieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'

export interface ITextareaFieldProps extends IFieldProps {
  autoresize?: boolean
  resize?: boolean
  rows?: number
  maxrows?: number
  loading?: boolean
  loadingIcon?: any
}

export type ITextareaField = IFormFieldBase<INPUT_TYPES.TEXTAREA, ITextareaFieldProps, {
  change?: (value: string) => void
}>
