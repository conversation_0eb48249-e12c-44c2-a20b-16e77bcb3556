<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="value?.name"
    :ui="{
      content: 'max-w-3xl',
    }"
  >
    <template #body>
      <div class="flex justify-center">
        <img
          v-if="value && isImageFromPath(value.path)"
          :src="value.url"
          alt="img-preview"
          class="max-h-96 max-w-full rounded-lg"
        />
        <video
          v-else-if="value && isVideoFromPath(value.path)"
          :src="value.url"
          controls
          class="max-h-96 max-w-full"
        />
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { isImageFromPath, isVideoFromPath } from '#core/helpers/componentHelper'
import type { IFileValue } from '#core/components/Form/types'

interface Props {
  value?: IFileValue
}

defineProps<Props>()

const emits = defineEmits<{
  close: [boolean]
}>()
</script>
