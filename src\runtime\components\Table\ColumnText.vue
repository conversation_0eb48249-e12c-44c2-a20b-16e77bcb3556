<template>
  {{ getValue }}
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { TableColumn } from '@nuxt/ui'
import { StringHelper } from '#imports'

const props = defineProps<{
  value: any
  row: any
  column: TableColumn<any>
}>()

const getValue = computed<string>(() => {
  const value = props.value
  const max = props.column?.meta?.max

  if (max) {
    return StringHelper.truncate(value, max)
  }

  if (typeof value === 'string') {
    return value || '-'
  }

  return value ?? '-'
})
</script>
