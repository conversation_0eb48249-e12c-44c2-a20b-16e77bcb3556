import { reactive, computed } from 'vue'
import { ObjectHelper } from '../utils/ObjectHelper'

import {
  apiListHelper,
  type IListLoaderOptions,
  type IListRunLoaderOptions,
} from '../helpers/apiListHelper'
import type { IAPIListState } from '../types/lib' // Changed from apiListHelper to types/lib
import type { IUseListLoader } from '../helpers/apiObjectHelper'

export const useListLoader = <T = any, O = Record<string, any>>(
  loaderOptions: IListLoaderOptions<T, O>,
): IUseListLoader<T, O> => {
  const result = reactive<IAPIListState<T>>({
    items: [],
    status: ObjectHelper.createStatus(),
    options: {},
  }) as IAPIListState<T>

  const clear = () => {
    result.status = ObjectHelper.createStatus()
    result.items = []
    result.options = {}
  }

  const run = async (payload?: IListRunLoaderOptions<T, O>) => {
    await api<PERSON><PERSON><PERSON>elper<T, O>(
      () => result,
      (_data) => {
        result.status = _data
      },
      (_data) => {
        result.options = _data
      },
      (_data) => {
        result.items = _data
      },
      {
        ...loaderOptions,
        ...payload,
      },
    )
  }

  const setLoading = () => {
    result.items = []
    result.status = ObjectHelper.toLoadingStatus(result.status)
  }

  const setItems = (_items: T[]) => {
    result.items = _items
  }

  return {
    items: computed(() => result.items),
    status: computed(() => result.status),
    options: computed(() => result.options),
    run,
    clear,
    setItems,
    setLoading,
  }
}
