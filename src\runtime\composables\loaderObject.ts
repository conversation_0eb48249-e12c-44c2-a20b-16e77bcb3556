import { computed, reactive, type ComputedRef, type UnwrapRef } from 'vue'
import { ObjectHelper } from '../utils/ObjectHelper'

import {
  apiObjectHelper,
  type IObjectLoaderOptions,
  type IObjectRunLoaderOptions,
  type IUseObjectLoader,
} from '../helpers/apiObjectHelper'
import type { IAPIOptions, IStatus } from '../types/lib'

export const useObjectLoader = <T = any, B = any, O = Record<string, any>> (
  loaderOptions: IObjectLoaderOptions<T, B, O>,
): IUseObjectLoader<T, B, O> => {
  const state = reactive<{
    data: T | null
    status: IStatus
    options: IAPIOptions
  }> ({
    data: null,
    status: ObjectHelper.createStatus(),
    options: {},
  })

  const clear = () => {
    state.status = ObjectHelper.createStatus()
    state.data = null
  }

  const run = async (payload: IObjectRunLoaderOptions<T, B> = {}) => {
    await apiObject<PERSON>elper<T, B, O>(
      () => ({
        data: state.data as any,
        status: state.status,
        options: state.options,
      }),
      (_status: IStatus) => {
        state.status = _status
      },
      (_options: IAPIOptions) => {
        state.options = _options
      },
      (_data?: T) => {
        state.data = (_data ?? null) as UnwrapRef<T> | null
      },
      payload.data as B,
      {
        ...loaderOptions,
        ...payload,
      },
    )
  }

  const setLoading = () => {
    state.status = ObjectHelper.toLoadingStatus(state.status)
  }

  const setData = (_data: T | null) => {
    state.data = _data as UnwrapRef<T> | null
  }

  return {
    data: computed(() => state.data) as ComputedRef<T | null>,
    status: computed(() => state.status),
    options: computed(() => state.options),
    run,
    clear,
    setLoading,
    setData,
  }
}
