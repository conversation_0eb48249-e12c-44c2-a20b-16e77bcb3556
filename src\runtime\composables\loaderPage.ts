import { reactive, type UnwrapRef } from 'vue'
import { ObjectHelper } from '../utils/ObjectHelper'

import type { IAPIOptions, IPageOptions, IStatus } from '../types/lib'
import {
  apiAddHelper,
  apiDeleteHelper,
  apiFetchHelper,
  apiFindHelper,
  type IPageAddLoaderOptions,
  type IPageDeleteLoaderOptions,
  type IPageFetchLoaderOptions,
  type IPageFindLoaderOptions,
  type IPageLoaderOptions,
  type IPageUpdateLoaderOptions,
  type IUsePageLoader,
  updateHelper,
} from '../helpers/apiPageHelper'
import { useCoreConfig } from './useConfig'

/**
 * Initialize page options with default values
 */
export const initPageOptions = (options: {
  limit: number
  primary: string
}): IPageOptions => ({
  currentPageCount: 0,
  currentPage: 1,
  totalPage: 0,
  totalCount: 0,
  limit: options.limit,
  search: '',
  primary: options.primary,
})

/**
 * Create a page loader composable for handling paginated data with CRUD operations
 */
export const usePageLoader = <T extends Record<string, any>>(
  loaderOptions: IPageLoaderOptions<T>,
): IUsePageLoader<T> => {
  const config = useCoreConfig()

  // Create reactive objects for each operation type
  const fetch = reactive({
    status: ObjectHelper.createStatus(),
    items: [] as T[],
    options: initPageOptions({
      limit: config.limit_per_page!,
      primary: config.default_primary_key!,
    }),
  }) as any // Will add the method later

  const find = reactive({
    status: ObjectHelper.createStatus(),
    item: null as T | null,
    options: {} as IAPIOptions,
  }) as any // Will add the method later

  const add = reactive({
    status: ObjectHelper.createStatus(),
    item: null as T | null,
    options: {} as IAPIOptions,
  }) as any // Will add the method later

  const update = reactive({
    status: ObjectHelper.createStatus(),
    item: null as T | null,
    options: {} as IAPIOptions,
  }) as any // Will add the method later

  const del = reactive({ // Using 'del' as a variable name since 'delete' is a reserved keyword
    status: ObjectHelper.createStatus(),
    item: null as T | null,
    options: {} as IAPIOptions,
  }) as any // Will add the method later

  /**
   * Reset all states to their initial values
   */
  const clearAll = () => {
    // Reset status states
    find.status = ObjectHelper.createStatus()
    del.status = ObjectHelper.createStatus()
    add.status = ObjectHelper.createStatus()
    update.status = ObjectHelper.createStatus()
    fetch.status = ObjectHelper.createStatus()

    // Reset item states
    find.item = null
    del.item = null
    update.item = null
    add.item = null
    fetch.items = []

    // Reset options
    fetch.options = initPageOptions({
      limit: config.limit_per_page!,
      primary: config.default_primary_key!,
    })

    find.options = {}
    add.options = {}
    update.options = {}
    del.options = {}
  }

  /**
   * Set fetch state to loading
   */
  const fetchSetLoading = (): void => {
    fetch.items = []
    fetch.status = ObjectHelper.toLoadingStatus(fetch.status)
  }

  /**
   * Set find state to loading
   */
  const findSetLoading = (): void => {
    find.status = ObjectHelper.toLoadingStatus(find.status)
  }

  /**
   * Fetch paginated data
   * @param page - Page number to fetch
   * @param query - Search query
   * @param opts - Additional options
   */
  const fetchPage = async (page = 1, query = '', opts?: IPageFetchLoaderOptions): Promise<void> => {
    await apiFetchHelper<T>(
      () => ({
        status: fetch.status,
        items: fetch.items,
        options: fetch.options,
      }),
      (data: IStatus) => {
        fetch.status = data
      },
      (data: IPageOptions) => {
        fetch.options = data
      },
      (data: T[]) => {
        fetch.items = data as UnwrapRef<T[]>
      },
      page,
      query,
      {
        ...loaderOptions,
        ...opts,
      },
    )
  }

  /**
   * Find a specific item by ID
   * @param id - Item ID
   * @param opts - Additional options
   */
  const findRun = async (id: string | number, opts?: IPageFindLoaderOptions): Promise<void> => {
    await apiFindHelper<T>(
      () => ({
        status: find.status,
        data: find.item as T,
        options: find.options,
      }),
      (data: IStatus) => {
        find.status = data
      },
      (data: IAPIOptions) => {
        find.options = data
      },
      (data: T | null) => {
        find.item = data
      },
      id,
      {
        ...loaderOptions,
        ...opts,
      },
    )
  }

  const addRun = async (payload: IPageAddLoaderOptions<Partial<T> | Record<string, any>>): Promise<void> => {
    await apiAddHelper<T>(
      () => ({
        items: fetch.items,
        status: add.status,
        data: add.item as T,
        options: add.options,
      }),
      (data: IStatus) => {
        add.status = data
      },
      (data: IAPIOptions) => {
        add.options = data
      },
      (data: T | null) => {
        add.item = data
      },
      (data: T[]) => {
        fetch.items = data as UnwrapRef<T[]>
      },
      payload.data,
      {
        ...loaderOptions,
        ...payload,
      },
    )
  }

  const updateRun = async (
    id: string | number,
    payload: IPageUpdateLoaderOptions<Partial<T> | Record<string, any>>): Promise<void> => {
    await updateHelper<T>(
      () => ({
        items: fetch.items,
        status: update.status,
        data: update.item as T,
        oldData: find.item as T,
        options: update.options,
      }),
      (data: IStatus) => {
        update.status = data
      },
      (data: IAPIOptions) => {
        update.options = data
      },
      (data: T) => {
        update.item = data as UnwrapRef<T>
      },
      (data: T[]) => {
        fetch.items = data as UnwrapRef<T[]>
      },
      (data: T) => {
        find.item = data as UnwrapRef<T>
      },
      id,
      payload.data,
      {
        ...loaderOptions,
        ...payload,
      },
    )
  }

  /**
   * Search for items
   * @param query - Search query
   * @param opts - Additional options
   */
  const fetchSearch = async (query = '', opts?: IPageFetchLoaderOptions): Promise<void> => {
    await fetchPage(1, query, opts)
  }

  /**
   * Change to a specific page while preserving the current search query
   * @param page - Page number to change to
   * @param opts - Additional options
   */
  const fetchPageChange = async (page: number, opts?: IPageFetchLoaderOptions): Promise<void> => {
    const {
      page: _page, ...params
    } = fetch.options?.request?.params || {}

    await fetchPage(page, '', {
      params: params,
      ...opts,
    })
  }

  /**
   * Remove an item
   * @param id - Item ID
   * @param opts - Additional options
   */
  const deleteRun = async (id: string | number, opts?: IPageDeleteLoaderOptions): Promise<void> => {
    await apiDeleteHelper<T>(
      () => ({
        status: del.status,
        data: del.item as T,
        items: fetch.items,
        options: del.options,
      }),
      (data: IStatus) => {
        del.status = data
      },
      (data: IAPIOptions) => {
        del.options = data
      },
      (data: T) => {
        del.item = data as UnwrapRef<T>
      },
      (data: T[]) => {
        fetch.items = data as UnwrapRef<T[]>
      },
      id,
      {
        ...loaderOptions,
        ...opts,
      },
    )
  }

  /**
   * Return the page loader composable with all state and methods
   */
  return {
    // State and methods
    fetch,
    find,
    add,
    update,
    delete: del, // Rename del to delete in the return object

    fetchPage: fetchPage,
    fetchSearch: fetchSearch,
    fetchPageChange: fetchPageChange,
    fetchSetLoading: fetchSetLoading,
    findSetLoading: findSetLoading,
    findRun: findRun,
    addRun: addRun,
    updateRun: updateRun,
    deleteRun: deleteRun,
    clearAll,
  }
}
