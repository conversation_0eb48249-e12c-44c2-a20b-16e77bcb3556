import { defineStore } from 'pinia'
import type { BreadcrumbItem, NavigationMenuItem } from '@nuxt/ui'

export interface IAppPageMeta {
  title?: string
  sub_title?: string
  isHideBreadcrumbs?: boolean
  breadcrumbs?: BreadcrumbItem[]
}

export interface IApp {
  pageMeta: IAppPageMeta
  sidebar: NavigationMenuItem[]
}

export const useApp = defineStore('_app', {
  state: (): IApp => ({
    pageMeta: {},
    sidebar: [],
  }),
  actions: {
    definePage(pageMeta: IAppPageMeta) {
      this.pageMeta = pageMeta as any
    },
    defineSidebar(items: BreadcrumbItem[]) {
      this.sidebar = items as any
    },
    patchTitle(payload: {
      title?: string
      sub_title?: string
    }) {
      this.pageMeta.title = payload.title
      this.pageMeta.sub_title = payload.sub_title
    },
    patchBreadcrumbs(payload: {
      breadcrumbs?: BreadcrumbItem[]
      isHideBreadcrumbs?: boolean
    }) {
      this.pageMeta.breadcrumbs = payload.breadcrumbs || [] as any
      this.pageMeta.isHideBreadcrumbs = payload.isHideBreadcrumbs || false
    },
  },
})
