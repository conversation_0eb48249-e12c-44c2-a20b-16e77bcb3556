import Dialog from '../components/Dialog/index.vue'
import { useOverlay } from '#imports'

export const enum DialogType {
  ERROR = 'error',
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  LOADING = 'loading',
}

export interface IDialogMetaItem {
  title: string
  description?: string
  icon?: string
  type?: DialogType
  confirmText?: string
  cancelText?: string
  isShowCancelBtn?: boolean
  isHideIcon?: boolean
  isConfirm?: boolean
}

export interface IDialog {
  error: (payload: IDialogMetaItem) => Promise<boolean>
  info: (payload: IDialogMetaItem) => Promise<boolean>
  success: (payload: IDialogMetaItem) => Promise<boolean>
  warning: (payload: IDialogMetaItem) => Promise<boolean>
  confirm: (payload: IDialogMetaItem) => Promise<boolean>
  loading: (payload?: Partial<IDialogMetaItem>) => Promise<boolean>
  close: () => void
}

export const useDialog = (): IDialog => {
  const overlay = useOverlay()
  const modalLoading = overlay.create(Dialog)

  const openDialog = async (payload: IDialogMetaItem): Promise<any> => {
    const modal = overlay.create(Dialog)
    const result = await modal.open(payload)

    const res = await result.result

    return new Promise((resolve, reject) => {
      if (res) {
        resolve(true)
      }

      reject(false)
    })
  }

  const error = async (payload: IDialogMetaItem) => {
    return openDialog({
      ...payload,
      type: DialogType.ERROR,
    })
  }

  const success = async (payload: IDialogMetaItem) => {
    return openDialog({
      ...payload,
      type: DialogType.SUCCESS,
    })
  }

  const info = async (payload: IDialogMetaItem) => {
    return openDialog({
      ...payload,
      type: DialogType.INFO,
    })
  }

  const warning = async (payload: IDialogMetaItem) => {
    return openDialog({
      ...payload,
      type: DialogType.WARNING,
    })
  }

  const confirm = async (payload: IDialogMetaItem) => {
    return openDialog({
      ...payload,
      type: DialogType.INFO,
      isShowCancelBtn: true,
      isConfirm: true,
    })
  }

  const loading = async (payload: Partial<IDialogMetaItem> = {}): Promise<any> => {
    modalLoading.open({
      ...payload as IDialogMetaItem,
      title: payload.title || 'กำลังโหลด...',
      type: DialogType.LOADING,
    })
  }

  const close = () => {
    modalLoading.close()
  }

  return {
    error,
    success,
    info,
    warning,
    confirm,
    loading,
    close,
  }
}
