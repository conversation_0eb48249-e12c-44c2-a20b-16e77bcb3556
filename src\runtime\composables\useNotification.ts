import { useToast } from '#imports'
import type { Toast } from '#ui/composables/useToast'

export const useNotification = () => {
  const toast = useToast()

  const info = (notification: Partial<Toast>) => {
    toast.add({
      icon: 'ph:info',
      color: 'info',
      ...notification,
    })
  }

  const success = (notification: Partial<Toast>) => {
    toast.add({
      icon: 'ph:check-circle',
      color: 'success',
      ...notification,
    })
  }

  const warning = (notification: Partial<Toast>) => {
    toast.add({
      icon: 'ph:warning',
      color: 'warning',
      ...notification,
    })
  }

  const error = (notification: Partial<Toast>) => {
    toast.add({
      icon: 'ph:x-circle',
      color: 'error',
      ...notification,
    })
  }

  return {
    info,
    success,
    warning,
    error,
    remove: toast.remove,
  }
}
