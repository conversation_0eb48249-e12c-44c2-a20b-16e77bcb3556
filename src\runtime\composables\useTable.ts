import type { ComputedRef } from 'vue'
import { get } from '@vueuse/core'
import type { Store } from 'pinia'
import type {
  TableColumn, // Using local TableColumn
  ISimpleTableOptions,
  ITableOptions,
} from '../components/Table/types'
import type { IUsePageLoader } from '../helpers/apiPageHelper'
import { computed, type IStatus, ObjectHelper } from '#imports'

export interface IUseTable<T extends Record<string, any>> {
  repo: IUsePageLoader<T> | Store<any, any>
  columns: () => TableColumn<T>[]
  options?: (() => Partial<ITableOptions<T>>) | Partial<ITableOptions<T>>
  transformItems?: (items: T[]) => T[]
}

export interface IUseTableSimple<T extends Record<string, any>> {
  items: () => T[]
  status?: () => IStatus
  columns: () => TableColumn<T>[]
  options?: (() => Partial<ISimpleTableOptions<T>>) | Partial<ISimpleTableOptions<T>>
}

export const useTable = <T extends Record<string, any>> (options: IUseTable<T>): ComputedRef<ITableOptions<T>> =>
  computed<ITableOptions<T>>(() => {
    return createTableOptions<T>(
      options.repo,
      options.columns(),
      typeof options.options === 'function'
        ? options.options()
        : (options.options ?? {}),
      options.transformItems,
    )
  })

export const useTableSimple = <T extends Record<string, any>> (
  options: IUseTableSimple<T>,
): ComputedRef<ISimpleTableOptions<T>> =>
  computed<ISimpleTableOptions<T>>(() => {
    return {
      items: options.items(),
      columns: options.columns(),
      rawData: options.items(),
      status: options.status ? options.status() : ObjectHelper.createStatus(),
      ...(typeof options.options === 'function'
        ? options.options()
        : (options.options ?? {})),
    }
  })

export const createTableOptions = <T extends Record<string, any>> (
  repo: IUsePageLoader<T>,
  columns: TableColumn<T>[],
  options: Partial<ITableOptions<T>>,
  transformItems?: (items: T[]) => T[],
): ITableOptions<T> => {
  return {
    rawData: transformItems
      ? transformItems(get(repo.fetch.items) as T[])
      : (get(repo.fetch.items) as T[]),
    pageOptions: get(repo.fetch.options),
    columns,
    status: get(repo.fetch.status),
    isRouteChange: false,
    ...options,
  }
}
