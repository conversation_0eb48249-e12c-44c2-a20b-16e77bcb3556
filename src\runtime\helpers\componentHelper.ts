import type { Ref } from 'vue'
import type { AxiosProgressEvent } from 'axios'
import { computed, ref } from '#imports'

export const checkMaxSize = (file: File, acceptFileSize: number = 0): boolean => {
  if (acceptFileSize) {
    return file.size / 1000 <= acceptFileSize
  }

  return true
}

export const checkFileType = (file: File, acceptFileType: string | string[]): boolean => {
  if (!acceptFileType || (Array.isArray(acceptFileType) && acceptFileType.length === 0)) return true

  const result: boolean[] = []
  const acceptedTypes = Array.isArray(acceptFileType)
    ? acceptFileType
    : acceptFileType.split(',').map((type) => type.trim())

  for (const acceptedType of acceptedTypes) {
    if (acceptedType.startsWith('.')) {
      const fileExtension = `.${file.name.split('.').pop()}`
      if (fileExtension.toLowerCase() === acceptedType.toLowerCase()) result.push(true)
    } else {
      const fileType = acceptedType.split('/')
      const fileExtension = file.type.split('/')

      if (fileType.length === 2 && fileExtension.length === 2) {
        if (fileType[1] === '*') {
          result.push(fileType[0].toLowerCase() === fileExtension[0].toLowerCase())
        }

        result.push(acceptedType.toLowerCase() === file.type.toLowerCase())
      }
    }
  }

  return result.includes(true)
}

export const generateURL = (file: File) => {
  const fileSrc = URL.createObjectURL(file)

  setTimeout(() => {
    URL.revokeObjectURL(fileSrc)
  }, 1000)

  return fileSrc
}

export const isImage = (file: File) => {
  return file.type.startsWith('image/')
}

export const isImageFromPath = (path: string = ''): boolean => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const extension = path.toLowerCase().substring(path.lastIndexOf('.'))

  return imageExtensions.includes(extension)
}

export const isVideoFromPath = (path: string = ''): boolean => {
  const imageExtensions = ['.mp4']
  const extension = path.toLowerCase().substring(path.lastIndexOf('.'))

  return imageExtensions.includes(extension)
}

export const isPDFFromPath = (path: string = ''): boolean => {
  const imageExtensions = ['.pdf']
  const extension = path.toLowerCase().substring(path.lastIndexOf('.'))

  return imageExtensions.includes(extension)
}

export const useFileAllocate = (
  selectedFile: Ref<File | undefined | null>,
  props: {
    accept?: string | string[]
    maxSize?: number
  },
) => {
  const selectedFileSizeKb = computed(() => ((selectedFile.value?.size || 0) / 1000).toFixed(2))
  const selectedFileSizeMb = computed(() =>
    ((selectedFile.value?.size || 0) / 1000 / 1000).toFixed(2),
  )

  const isSelectedFileUseMb = computed(() => (selectedFile.value?.size || 0) / 1000 > 1024)

  const acceptFileSizeKb = computed(() => props.maxSize || 0)
  const acceptFileSizeMb = computed(() => ((acceptFileSizeKb.value || 0) / 1024).toFixed(2))
  const isAcceptFileUseMb = computed(() => acceptFileSizeKb.value && acceptFileSizeKb.value > 1024)
  const acceptFile = computed(() =>
    typeof props.accept === 'string' ? props.accept : props.accept?.join(','),
  )

  return {
    selectedFileSizeKb,
    selectedFileSizeMb,
    isSelectedFileUseMb,
    acceptFileSizeKb,
    acceptFileSizeMb,
    isAcceptFileUseMb,
    acceptFile,
  }
}

export const useFileSize = (size: number = 0) => {
  const isSelectedFileUseMb = computed(() => size / 1000 > 1024)
  const selectedFileSizeKb = computed(() => (size / 1000).toFixed(2))
  const selectedFileSizeMb = computed(() => (size / 1000 / 1000).toFixed(2))

  return {
    isSelectedFileUseMb,
    selectedFileSizeKb,
    selectedFileSizeMb,
  }
}

export const useFileProgress = () => {
  const percent = ref<number>(0)

  const onUploadProgress = (progressEvent: AxiosProgressEvent) => {
    if (!progressEvent.total || progressEvent.total === 0) {
      percent.value = 100

      return
    }

    percent.value = (Math.floor((progressEvent.loaded * 100) / progressEvent.total) || 0) * 0.8
  }

  const onDownloadProgress = (progressEvent: AxiosProgressEvent) => {
    if (!progressEvent.total || progressEvent.total === 0) {
      percent.value = 100

      return
    }

    percent.value = Math.floor((progressEvent.loaded * 100) / progressEvent.total) * 0.2 + 80
  }

  return {
    percent,
    onUploadProgress,
    onDownloadProgress,
  }
}

export const downloadFileFromURL = async (url: string, filename?: string) => {
  return new Promise<void>((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    xhr.responseType = 'blob'

    xhr.onload = () => {
      const a = document.createElement('a')
      const url = window.URL.createObjectURL(xhr.response)

      a.href = url
      a.download = filename || url.split('/').pop() || new Date().toISOString()
      a.click()
      window.URL.revokeObjectURL(url)
      resolve()
    }

    xhr.onerror = () => {
      reject()
    }

    xhr.open('GET', url)
    xhr.send()
  })
}
