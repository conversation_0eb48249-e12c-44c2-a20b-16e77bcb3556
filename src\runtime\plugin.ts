import { vMaska } from 'maska/vue'
import * as v from 'valibot'
import { defineNuxtPlugin } from '#app'
import { valibotLanguageTH } from '#core/i18n/valibot'

export default defineNuxtPlugin((_nuxtApp) => {
  v.setGlobalMessage((issue) => {
    const messages = valibotLanguageTH

    const getMessage = messages[issue.type] || messages.default

    return getMessage(issue)
  })

  _nuxtApp.vueApp.directive('maska', vMaska)
})
