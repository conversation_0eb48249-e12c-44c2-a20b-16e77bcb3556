@import "tailwindcss";
@import "@nuxt/ui";
@plugin '@tailwindcss/typography';
@source inline("prose");


@theme {
  --font-sans: 'Noto Sans Thai', 'Noto Sans Thai Looped', 'Public Sans', sans-serif;
}

@theme static {

  --color-main: #232C5A;
  --color-main-50: #F4F4F7;
  --color-main-100: #E9EAEF;
  --color-main-200: #C8CAD6;
  --color-main-300: #A7ABBD;
  --color-main-400: #656B8C;
  --color-main-500: #232C5A;
  --color-main-600: #202851;
  --color-main-700: #151A36;
  --color-main-800: #101429;
  --color-main-900: #0B0D1B;
  --color-main-950: #0B0D1B;

  --color-secondary: #EE8B36;
  --color-secondary-50: #fdf1e7;
  --color-secondary-100: #f9d6b8;
  --color-secondary-200: #f5bb89;
  --color-secondary-300: #f1a05a;
  --color-secondary-400: #ed852b;
  --color-secondary-500: #d46b12;
  --color-secondary-600: #a5540e;
  --color-secondary-700: #763c0a;
  --color-secondary-800: #472406;
  --color-secondary-900: #180c02;

  --color-info: #0D8CEE;
  --color-info-50: #F3F9FE;
  --color-info-100: #E7F4FD;
  --color-info-200: #EBF6FF;
  --color-info-300: #9ED1F8;
  --color-info-400: #56AFF3;
  --color-info-500: #0D8CEE;
  --color-info-600: #0C7ED6;
  --color-info-700: #08548F;
  --color-info-800: #063F6B;
  --color-info-900: #042A47;


  --color-error: #F25555;
  --color-error-50: #FEF7F7;
  --color-error-100: #FEEEEE;
  --color-error-200: #FFDFDF;
  --color-error-300: #FABBBB;
  --color-error-400: #F68888;
  --color-error-500: #F25555;
  --color-error-600: #DA4D4D;
  --color-error-700: #913333;
  --color-error-800: #6D2626;
  --color-error-900: #491A1A;

  --color-success: #3FB061;
  --color-success-50: #F5FBF7;
  --color-success-100: #ECF7EF;
  --color-success-200: #DAEEE0;
  --color-success-300: #B2DFC0;
  --color-success-400: #79C890;
  --color-success-500: #3FB061;
  --color-success-600: #399E57;
  --color-success-700: #266A3A;
  --color-success-800: #1C4F2C;
  --color-success-900: #13351D;

  --color-warning: #FF9A35;
  --color-warning-50: #FFFAF5;
  --color-warning-100: #FFF5EB;
  --color-warning-200: #FEF1CC;
  --color-warning-300: #FFD7AE;
  --color-warning-400: #FFB872;
  --color-warning-500: #FF9A35;
  --color-warning-600: #E68B30;
  --color-warning-700: #995C20;
  --color-warning-800: #734518;
  --color-warning-900: #4D2E10;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgb(0 0 0 / 30%);
  box-shadow: 0 0 1px rgb(255 255 255 / 50%);
}

:root {
  --dp-font-family: inherit !important;
}

.dp__theme_light {
  --dp-primary-color: var(--color-main) !important;
  --dp-primary-disabled-color: var(--color-main-200) !important;
}


#__nuxt,
body,
html {
  @apply w-full h-full;
}
