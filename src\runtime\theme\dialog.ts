export const dialogTheme = {
  icons: {
    iconSuccess: 'i-heroicons-check-circle',
    iconInfo: 'i-heroicons-information-circle',
    iconWarning: 'i-heroicons-exclamation-circle',
    iconError: 'i-heroicons-x-circle',
    iconConfirm: 'i-heroicons-information-circle',
    iconLoading: 'i-svg-spinners:180-ring-with-bg',
  },
  slots: {
    base: 'flex space-x-4 shadow-lg ring ring-default z-[100] text-sm fixed top-[50%] left-[50%] max-h-[85vh] w-[90vw] max-w-[500px] translate-x-[-50%] translate-y-[-50%] rounded-lg bg-white p-[25px] focus:outline-none',
    overlay: 'fixed inset-0 bg-elevated/75 backdrop-blur',
    icon: 'size-12',
    wrapper: 'flex flex-col w-full',
    confirmColor: 'info',
    title: 'font-bold text-lg',
    description: 'text-neutral-400 text-sm',
    buttonGroup: 'flex justify-end items-center space-x-3 mt-4',
    confirmButton: '',
    cancelButton: '',
  },
  variants: {
    color: {
      success: {
        icon: 'text-success',
      },
      info: {
        icon: 'text-info',
      },
      warning: {
        icon: 'text-warning',
      },
      error: {
        icon: 'text-error',
      },
      loading: {
        icon: 'text-primary',
        base: 'max-w-[400px]',
        wrapper: 'justify-center',
      },
    },
    confirm: {
      true: {
        icon: 'text-info',
      },
      false: {},
    },
  },
  defaultVariants: { },
}
