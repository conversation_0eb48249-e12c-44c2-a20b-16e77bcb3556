export const wysiwygTheme = {
  slots: {
    container: 'border border-gray-200 rounded focus:ring-primary-500 relative block w-full resize-none rounded-md border-0 bg-white p-0 pb-3 text-sm text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:outline-none focus:ring-2 disabled:cursor-not-allowed disabled:opacity-75',
    toolbar: 'flex flex-wrap border py-2 px-2 gap-1 border-gray-300 bg-white rounded-t-md',
    toolbarGroup: 'flex items-center border-r border-gray-200 pr-2',
    menuItem: 'px-1 py-1 rounded-md hover:bg-gray-100 transition-colors flex justify-center items-center cursor-pointer flex-wrap',
    menuItemActive: 'bg-primary-100 text-primary-600',
    icon: 'size-5',
    editorContent: '',
  },
  variants: {
    size: {
      xs: {
        button: 'px-1.5 py-0.5 text-xs',
        icon: 'h-3 w-3',
        select: 'px-1.5 py-0.5 text-xs',
      },
      sm: {
        button: 'px-2 py-1 text-sm',
        icon: 'h-3.5 w-3.5',
        select: 'px-2 py-1 text-xs',
      },
      md: {
        button: 'px-2 py-1 text-sm',
        icon: 'h-4 w-4',
        select: 'px-2 py-1 text-xs',
      },
      lg: {
        button: 'px-3 py-1.5 text-base',
        icon: 'h-5 w-5',
        select: 'px-3 py-1.5 text-sm',
      },
      xl: {
        button: 'px-4 py-2 text-lg',
        icon: 'h-6 w-6',
        select: 'px-4 py-2 text-base',
      },
    },
    color: {
      primary: {
        button: 'hover:bg-blue-50 hover:border-blue-300',
      },
      gray: {
        button: 'hover:bg-gray-100 hover:border-gray-300',
      },
    },
  },
  compoundVariants: [],
  defaultVariants: {
    size: 'md',
    color: 'gray',
  },
}
