import { describe, test, expect } from 'vitest'
import { NumberHel<PERSON> } from './NumberHelper'

describe('NumberHelper', () => {
  describe('withComma', () => {
    test('formats integers with locale commas', () => {
      expect(NumberHelper.withComma(1000)).toBe('1,000')
      expect(NumberHelper.withComma(123456)).toBe('123,456')
      expect(NumberHelper.withComma(1000000)).toBe('1,000,000')
    })

    test('formats decimal numbers with locale commas', () => {
      expect(NumberHelper.withComma(1234.56)).toBe('1,234.56')
      expect(NumberHelper.withComma(123456.789)).toBe('123,456.789')
    })

    test('formats string numbers', () => {
      expect(NumberHelper.withComma('1000')).toBe('1,000')
      expect(NumberHelper.withComma('123456.78')).toBe('123,456.78')
    })

    test('handles zero values', () => {
      expect(NumberHelper.withComma(0)).toBe('0')
      expect(NumberHelper.withComma('0')).toBe('0')
    })

    test('handles negative numbers', () => {
      expect(NumberHelper.withComma(-1000)).toBe('-1,000')
      expect(NumberHelper.withComma('-123456.78')).toBe('-123,456.78')
    })

    test('handles edge cases', () => {
      expect(NumberHelper.withComma()).toBe('0')
      expect(NumberHelper.withComma(null as any)).toBe('0')
      expect(NumberHelper.withComma(undefined)).toBe('0')
      expect(NumberHelper.withComma('')).toBe('0')
      expect(NumberHelper.withComma('invalid')).toBe('NaN')
    })

    test('handles very large numbers', () => {
      expect(NumberHelper.withComma(999999999999)).toBe('999,999,999,999')
    })

    test('handles very small decimal numbers', () => {
      expect(NumberHelper.withComma(0.001)).toBe('0.001')
      expect(NumberHelper.withComma(0.123456789)).toBe('0.123')
    })
  })

  describe('withFixed', () => {
    test('formats integers with up to 2 decimal places', () => {
      expect(NumberHelper.withFixed(100)).toBe('100')
      expect(NumberHelper.withFixed(1000)).toBe('1,000')
    })

    test('formats decimal numbers with up to 2 decimal places', () => {
      expect(NumberHelper.withFixed(100.5)).toBe('100.5')
      expect(NumberHelper.withFixed(100.50)).toBe('100.5')
      expect(NumberHelper.withFixed(100.123)).toBe('100.12')
      expect(NumberHelper.withFixed(100.999)).toBe('101')
    })

    test('handles rounding correctly', () => {
      expect(NumberHelper.withFixed(100.126)).toBe('100.13')
      expect(NumberHelper.withFixed(100.124)).toBe('100.12')
      expect(NumberHelper.withFixed(100.995)).toBe('101')
    })

    test('formats string numbers', () => {
      expect(NumberHelper.withFixed('100.123')).toBe('100.12')
      expect(NumberHelper.withFixed('1000.5')).toBe('1,000.5')
    })

    test('handles zero values', () => {
      expect(NumberHelper.withFixed(0)).toBe('0')
      expect(NumberHelper.withFixed('0')).toBe('0')
      expect(NumberHelper.withFixed(0.00)).toBe('0')
    })

    test('handles negative numbers', () => {
      expect(NumberHelper.withFixed(-100.123)).toBe('-100.12')
      expect(NumberHelper.withFixed('-1000.999')).toBe('-1,001')
    })

    test('handles edge cases', () => {
      expect(NumberHelper.withFixed()).toBe('0')
      expect(NumberHelper.withFixed(null as any)).toBe('0')
      expect(NumberHelper.withFixed(undefined)).toBe('0')
      expect(NumberHelper.withFixed('')).toBe('0')
      expect(NumberHelper.withFixed('invalid')).toBe('NaN')
    })

    test('handles very small numbers', () => {
      expect(NumberHelper.withFixed(0.001)).toBe('0')
      expect(NumberHelper.withFixed(0.005)).toBe('0.01')
      expect(NumberHelper.withFixed(0.999)).toBe('1')
    })

    test('handles large numbers with decimals', () => {
      expect(NumberHelper.withFixed(123456.789)).toBe('123,456.79')
      expect(NumberHelper.withFixed(999999.999)).toBe('1,000,000')
    })
  })

  describe('toCurrency', () => {
    test('formats numbers with 2 decimal places when no currency provided', () => {
      expect(NumberHelper.toCurrency(100)).toBe('100.00')
      expect(NumberHelper.toCurrency(1000)).toBe('1,000.00')
      expect(NumberHelper.toCurrency(123456)).toBe('123,456.00')
    })

    test('formats decimal numbers with exactly 2 decimal places', () => {
      expect(NumberHelper.toCurrency(100.5)).toBe('100.50')
      expect(NumberHelper.toCurrency(100.123)).toBe('100.12')
      expect(NumberHelper.toCurrency(100.999)).toBe('101.00')
    })

    test('formats string numbers', () => {
      expect(NumberHelper.toCurrency('100')).toBe('100.00')
      expect(NumberHelper.toCurrency('100.50')).toBe('100.50')
      expect(NumberHelper.toCurrency('123456.789')).toBe('123,456.79')
    })

    test('handles zero values', () => {
      expect(NumberHelper.toCurrency(0)).toBe('0.00')
      expect(NumberHelper.toCurrency('0')).toBe('0.00')
    })

    test('handles negative numbers', () => {
      expect(NumberHelper.toCurrency(-100)).toBe('-100.00')
      expect(NumberHelper.toCurrency(-100.50)).toBe('-100.50')
      expect(NumberHelper.toCurrency('-123.45')).toBe('-123.45')
    })

    test('handles edge cases', () => {
      expect(NumberHelper.toCurrency()).toBe('0.00')
      expect(NumberHelper.toCurrency(null as any)).toBe('0.00')
      expect(NumberHelper.toCurrency(undefined)).toBe('0.00')
      expect(NumberHelper.toCurrency('')).toBe('0.00')
    })

    test('handles invalid input gracefully', () => {
      const result = NumberHelper.toCurrency('invalid')

      expect(result).toBe('NaN')
    })

    test('handles large amounts', () => {
      expect(NumberHelper.toCurrency(1000000)).toBe('1,000,000.00')
      expect(NumberHelper.toCurrency(999999999.99)).toBe('999,999,999.99')
    })

    test('handles rounding correctly', () => {
      expect(NumberHelper.toCurrency(100.126)).toBe('100.13')
      expect(NumberHelper.toCurrency(100.124)).toBe('100.12')
      expect(NumberHelper.toCurrency(100.995)).toBe('101.00')
    })

    test('formats with currency when currency parameter provided', () => {
      const result1 = NumberHelper.toCurrency(100, 'USD')
      const result2 = NumberHelper.toCurrency(1000, 'THB')
      const result3 = NumberHelper.toCurrency(123.45, 'EUR')

      expect(result1).toContain('100.00')
      expect(result2).toContain('1,000.00')
      expect(result3).toContain('123.45')

      // Should contain currency formatting when currency is provided
      expect(typeof result1).toBe('string')
      expect(typeof result2).toBe('string')
      expect(typeof result3).toBe('string')
    })

    test('currency parameter enables currency style formatting', () => {
      const withoutCurrency = NumberHelper.toCurrency(100)
      const withCurrency = NumberHelper.toCurrency(100, 'USD')

      expect(withoutCurrency).toBe('100.00')
      expect(withCurrency).not.toBe('100.00') // Should be different with currency formatting
      expect(withCurrency).toContain('100.00')
    })

    test('returns string format', () => {
      const result = NumberHelper.toCurrency(100)

      expect(typeof result).toBe('string')
      expect(result.length).toBeGreaterThan(0)
    })
  })

  describe('toNumber', () => {
    test('converts valid number strings to numbers', () => {
      expect(NumberHelper.toNumber('100')).toBe(100)
      expect(NumberHelper.toNumber('100.5')).toBe(100.5)
      expect(NumberHelper.toNumber('-50')).toBe(-50)
      expect(NumberHelper.toNumber('0')).toBe(0)
    })

    test('returns numbers as-is', () => {
      expect(NumberHelper.toNumber(100)).toBe(100)
      expect(NumberHelper.toNumber(100.5)).toBe(100.5)
      expect(NumberHelper.toNumber(-50)).toBe(-50)
      expect(NumberHelper.toNumber(0)).toBe(0)
    })

    test('handles boolean values', () => {
      expect(NumberHelper.toNumber(true)).toBe(1)
      expect(NumberHelper.toNumber(false)).toBe(0)
    })

    test('returns 0 for invalid inputs', () => {
      expect(NumberHelper.toNumber(null)).toBe(0)
      expect(NumberHelper.toNumber(undefined)).toBe(0)
      expect(NumberHelper.toNumber('')).toBe(0)
      expect(NumberHelper.toNumber('invalid')).toBe(0)
      expect(NumberHelper.toNumber({})).toBe(0)
      expect(NumberHelper.toNumber([])).toBe(0)
    })

    test('handles special number values', () => {
      expect(NumberHelper.toNumber(Infinity)).toBe(Infinity)
      expect(NumberHelper.toNumber(-Infinity)).toBe(-Infinity)
      expect(NumberHelper.toNumber(Number.NaN)).toBe(0) // NaN || 0 = 0
    })

    test('handles whitespace in strings', () => {
      expect(NumberHelper.toNumber(' 100 ')).toBe(100)
      expect(NumberHelper.toNumber('  ')).toBe(0)
    })

    test('handles scientific notation', () => {
      expect(NumberHelper.toNumber('1e3')).toBe(1000)
      expect(NumberHelper.toNumber('1.5e2')).toBe(150)
      expect(NumberHelper.toNumber('1e-3')).toBe(0.001)
    })

    test('handles hexadecimal strings', () => {
      expect(NumberHelper.toNumber('0x10')).toBe(16)
      expect(NumberHelper.toNumber('0xFF')).toBe(255)
    })

    test('handles arrays and objects', () => {
      expect(NumberHelper.toNumber([1])).toBe(1)
      expect(NumberHelper.toNumber([1, 2])).toBe(0) // NaN || 0

      expect(NumberHelper.toNumber({
        valueOf: () => 42,
      })).toBe(42)

      expect(NumberHelper.toNumber({
        toString: () => '123',
      })).toBe(123)
    })
  })
})
