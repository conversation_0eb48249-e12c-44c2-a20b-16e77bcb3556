import { describe, it, expect } from 'vitest'
import type { AxiosError } from 'axios'
import { ObjectHelper } from './ObjectHelper'

describe('ObjectHelper', () => {
  describe('createOption', () => {
    it('should create an option object with the provided value and label', () => {
      const value = 'value1'
      const label = 'Label 1'
      const option = ObjectHelper.createOption(value, label)

      expect(option).toEqual({
        value,
        label,
      })
    })

    it('should create an option object with an empty label if not provided', () => {
      const value = 'value1'
      const option = ObjectHelper.createOption(value)

      expect(option).toEqual({
        value,
        label: '',
      })
    })
  })

  describe('toOption', () => {
    it('should create an option object from the provided data', () => {
      const data = {
        id: 1,
        name: 'Option 1',
      }

      const option = ObjectHelper.toOption(data)

      expect(option).toEqual({
        value: 1,
        label: 'Option 1',
      })
    })

    it('should use the provided value and label attributes', () => {
      const data = {
        code: 'CODE1',
        description: 'Description 1',
      }

      const option = ObjectHelper.toOption(data, 'code', 'description')

      expect(option).toEqual({
        value: 'CODE1',
        label: 'Description 1',
      })
    })
  })

  describe('toErrorStatus', () => {
    it('should create an error status object from the provided error', () => {
      const error: AxiosError = {
        response: {
          status: 400,
          request: {
            response: JSON.stringify({
              code: 'INVALID_REQUEST',
              message: 'Invalid request',
            }),
          },
        },
      } as AxiosError

      const status = ObjectHelper.toErrorStatus({}, error)

      expect(status.isError).toBe(true)
      expect(status.isSuccess).toBe(false)
      expect(status.errorData).toEqual({
        code: 'INVALID_REQUEST',
        message: 'Invalid request',
      })
    })

    it('should create a network error status if the error response status is missing', () => {
      const error: AxiosError = {} as AxiosError

      const status = ObjectHelper.toErrorStatus({}, error)

      expect(status.isError).toBe(true)
      expect(status.isSuccess).toBe(false)
      expect(status.errorData).toEqual({
        code: 'NETWORK_ERROR',
        message: 'Network error',
      })
    })
  })

  // Add more test cases for other methods...
})
