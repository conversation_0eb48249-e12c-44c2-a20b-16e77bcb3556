import type { AxiosError } from 'axios'
import type { IStatus } from '../types/lib'
import type { IOption } from '../types/common'
import { ParamHelper } from './ParamHelper'
import { _isEmpty, _get } from './lodash'

export class ObjectHelper {
  static createOption(value: any, label = ''): IOption {
    return {
      value,
      label,
    }
  }

  static toOption(data: any, valueAttr = 'id', labelAttr = 'name'): IOption {
    const newData = data || {}
    const value = _get(newData, valueAttr, '')

    return {
      value,
      label: _get(newData, labelAttr, value)!,
    }
  }

  static toOptions(
    data: any, valueAttr = 'id', labelAttr = 'name'): IOption[] {
    if (!data) {
      return []
    }

    const value = _get(data, valueAttr, '')

    return [
      {
        value,
        label: _get(data, labelAttr, value)!,
      },
    ]
  }

  static toStatus(obj: any): IStatus {
    return {
      isLoaded: ParamHelper.getBoolFalse(obj.isLoaded),
      isLoading: ParamHelper.getBoolFalse(obj.isLoading),
      isError: ParamHelper.getBoolFalse(obj.isError),
      isSuccess: ParamHelper.getBoolFalse(obj.isSuccess),
      errorData: obj.errorData || null,
    }
  }

  static toLoadingStatus(obj: any): any {
    return {
      ...obj,
      isLoaded: false,
      isError: false,
      isLoading: true,
      isSuccess: false,
    }
  }

  static toItemsSuccessStatus(obj: any, items: any[]): any {
    return {
      ...obj,
      isSuccess: true,
      errorData: null,
      items,
    }
  }

  static toObjectSuccessStatus(obj: any, data: any = null): any {
    return {
      ...obj,
      isSuccess: true,
      errorData: null,
      data,
    }
  }

  static toErrorStatus(obj: any, error: AxiosError | any): any {
    if (process.env.NODE_ENV !== 'production') {
      console.error('API ERROR', error)
    }

    let newError = {
      code: 'SOMETHING_WENT_WRONG',
      message: 'Something went wrong',
    }

    try {
      newError = JSON.parse(error.response?.request?.response || '{}')
    } catch (e) { /* empty */ }

    if (!error.response?.status) {
      newError = {
        code: 'NETWORK_ERROR',
        message: 'Network error',
      }
    }

    return {
      ...obj,
      isError: true,
      isSuccess: false,
      errorData: newError,
    }
  }

  static toSuccessStatus(obj: any): any {
    return {
      ...obj,
      isSuccess: true,
      errorData: null,
    }
  }

  static toCompleteStatus(obj: any): any {
    return {
      ...obj,
      isLoading: false,
      isLoaded: true,
    }
  }

  static createStatus(): IStatus {
    return {
      isLoaded: false,
      isLoading: false,
      isError: false,
      isSuccess: false,
      errorData: null,
    }
  }

  static isInvalidParams(errorData: any): boolean {
    return errorData.code === 'INVALID_PARAMS'
  }

  static isEmpty = (object: any): boolean => {
    return _isEmpty(object)
  }

  static stringArrayToObject = (array: string[]): Record<string, string> => {
    return array.reduce<Record<string, string>>((obj, item) => {
      obj[item] = item

      return obj
    }, {})
  }
}
