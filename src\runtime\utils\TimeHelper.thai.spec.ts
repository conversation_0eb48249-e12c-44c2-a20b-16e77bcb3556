import { assert, describe, it, vi } from 'vitest'
import { TimeHelper } from './TimeHelper' // Adjust the import path as needed

vi.mock('#core/composables/useConfig', () => ({
  useCoreConfig: () => ({
    date_format: 'yyyy-MM-dd',
    date_time_format: 'yyyy-MM-dd HH:mm',
    time_format: 'HH:mm',
    is_thai_year: true,
    time_zone: 'Asia/Bangkok',
  }),
}))

describe('TimeHelper', async () => {
  it('displayDate', () => {
    const inputTime = '2023-12-19 12:00' // Adjust the input time as needed
    const expectedOutput = '2566-12-19' // Adjust the expected output as needed

    const result = TimeHelper.displayDate(inputTime)

    assert.equal(result, expectedOutput)
  })

  it('displayDateTime', () => {
    const inputTime = '2023-12-19 12:00' // Adjust the input time as needed
    const expectedOutput = '2566-12-19 12:00' // Adjust the expected output as needed

    const result = TimeHelper.displayDateTime(inputTime)

    assert.equal(result, expectedOutput)
  })

  it('displayDateTime - iso', () => {
    const inputTime = '2023-01-16T22:00:00Z' // Adjust the input time as needed
    const expectedOutput = '2566-01-17 05:00' // Adjust the expected output as needed

    const result = TimeHelper.displayDateTime(inputTime)

    assert.equal(result, expectedOutput)
  })
})
